/**
 * QR Code Styling Provider
 * Implementation using the qr-code-styling library
 */

import { QRGenerationOptions, QRCustomizationOptions } from '../../shared/types/pix-types';
import { QRGenerationError } from '../../shared/errors/pix-errors';

declare global {
  interface Window {
    QRCodeStyling: any;
  }
}

export interface QRCodeStylingResult {
  element: HTMLElement;
  dataUrl?: string;
}

export class QRCodeStylingProvider {
  private static isLibraryAvailable(): boolean {
    return typeof window !== 'undefined' && typeof window.QRCodeStyling !== 'undefined';
  }

  async generateQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult> {
    if (!QRCodeStylingProvider.isLibraryAvailable()) {
      throw new QRGenerationError('QRCodeStyling library is not available');
    }

    try {
      const qrCode = new window.QRCodeStyling(options);
      
      // Create container
      const container = this.createTempContainer();
      
      // Generate QR code
      qrCode.append(container);
      
      // Wait for rendering
      await this.waitForRendering();
      
      // Get the generated element
      const qrElement = container.firstElementChild as HTMLElement;
      if (!qrElement) {
        throw new Error('Failed to generate QR code element');
      }

      // Clone element to avoid issues with DOM manipulation
      const clonedElement = qrElement.cloneNode(true) as HTMLElement;
      
      // Clean up
      this.cleanupContainer(container);
      
      const result: QRCodeStylingResult = {
        element: clonedElement
      };

      const dataUrl = await this.extractDataUrl(clonedElement);
      if (dataUrl) {
        result.dataUrl = dataUrl;
      }

      return result;

    } catch (error) {
      throw new QRGenerationError(
        `QRCodeStyling generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async generateZeroMarginQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult> {
    const modifiedOptions = { ...options, margin: 0 };
    const result = await this.generateQRCode(modifiedOptions);
    
    // Apply zero margin post-processing
    this.applyZeroMarginProcessing(result.element);
    
    return result;
  }

  async generateStandardQRCode(data: string): Promise<QRCodeStylingResult> {
    const standardOptions: QRGenerationOptions = {
      width: 300,
      height: 300,
      type: 'svg',
      data,
      margin: 10,
      qrOptions: {
        typeNumber: 0,
        errorCorrectionLevel: 'M'
      },
      dotsOptions: {
        color: '#000000',
        type: 'square',
        roundSize: true
      },
      backgroundOptions: {
        color: '#ffffff',
        round: 0
      },
      cornersSquareOptions: {
        color: '#000000'
      },
      cornersDotOptions: {
        color: '#000000'
      }
    };

    return this.generateQRCode(standardOptions);
  }

  async generateCustomizedQRCode(
    data: string,
    customization: QRCustomizationOptions,
    imageFile?: File
  ): Promise<QRCodeStylingResult> {
    const options = this.buildCustomizationOptions(data, customization);

    if (imageFile) {
      return this.generateWithCustomImage(options, imageFile);
    }

    return this.generateQRCode(options);
  }

  async generateWithCustomImage(
    options: QRGenerationOptions,
    imageFile: File
  ): Promise<QRCodeStylingResult> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const imageOptions = {
            crossOrigin: 'anonymous',
            margin: options.imageOptions?.margin || 0,
            imageSize: options.imageOptions?.imageSize || 0.4,
            hideBackgroundDots: options.imageOptions?.hideBackgroundDots || false
          };

          const modifiedOptions = {
            ...options,
            image: e.target?.result as string,
            imageOptions
          };

          const result = await this.generateQRCode(modifiedOptions);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      reader.onerror = () => reject(new Error('Failed to read image file'));
      reader.readAsDataURL(imageFile);
    });
  }

  private buildCustomizationOptions(data: string, customization: QRCustomizationOptions): QRGenerationOptions {
    // Create display options with constrained size (max 400px for display)
    const maxDisplaySize = 400;
    const exportSize = customization.qrSize;
    const displaySize = Math.min(exportSize, maxDisplaySize);

    return {
      width: displaySize,
      height: displaySize,
      type: 'canvas',
      data,
      margin: 10, // Standard margin for display
      qrOptions: {
        typeNumber: 0,
        mode: undefined,
        errorCorrectionLevel: 'M'
      },
      dotsOptions: {
        color: customization.dotsColor,
        type: customization.dotsType,
        roundSize: true
      },
      backgroundOptions: {
        color: customization.backgroundColor,
        round: 0
      },
      cornersSquareOptions: {
        color: customization.cornerSquareColor,
        type: customization.cornerSquareType
      },
      cornersDotOptions: {
        color: customization.cornerDotColor,
        type: customization.cornerDotType
      },
      imageOptions: {
        crossOrigin: 'anonymous',
        margin: customization.imageMargin,
        imageSize: customization.imageSize,
        hideBackgroundDots: customization.hideBackgroundDots,
        saveAsBlob: true
      }
    };
  }

  async generateForDownload(
    data: string,
    customization: QRCustomizationOptions,
    imageFile?: File
  ): Promise<QRCodeStylingResult> {
    const options = this.buildDownloadOptions(data, customization);

    if (imageFile) {
      return this.generateWithCustomImage(options, imageFile);
    }

    return this.generateQRCode(options);
  }

  private buildDownloadOptions(data: string, customization: QRCustomizationOptions): QRGenerationOptions {
    return {
      width: customization.qrSize,
      height: customization.qrSize,
      type: 'canvas',
      data,
      margin: 10,
      qrOptions: {
        typeNumber: 0,
        mode: undefined,
        errorCorrectionLevel: 'M'
      },
      dotsOptions: {
        color: customization.dotsColor,
        type: customization.dotsType,
        roundSize: true
      },
      backgroundOptions: {
        color: customization.backgroundColor,
        round: 0
      },
      cornersSquareOptions: {
        color: customization.cornerSquareColor,
        type: customization.cornerSquareType
      },
      cornersDotOptions: {
        color: customization.cornerDotColor,
        type: customization.cornerDotType
      },
      imageOptions: {
        crossOrigin: 'anonymous',
        margin: customization.imageMargin,
        imageSize: customization.imageSize,
        hideBackgroundDots: customization.hideBackgroundDots,
        saveAsBlob: true
      }
    };
  }

  private createTempContainer(): HTMLElement {
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    document.body.appendChild(container);
    return container;
  }

  private async waitForRendering(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, 100));
  }

  private cleanupContainer(container: HTMLElement): void {
    if (container.parentNode) {
      container.parentNode.removeChild(container);
    }
  }

  private async extractDataUrl(element: HTMLElement): Promise<string | undefined> {
    if (element.tagName.toLowerCase() === 'canvas') {
      const canvas = element as HTMLCanvasElement;
      return canvas.toDataURL();
    }

    if (element.tagName.toLowerCase() === 'svg') {
      return this.svgToDataUrl(element as unknown as SVGElement);
    }

    return undefined;
  }

  private svgToDataUrl(svg: SVGElement): string {
    const serializer = new XMLSerializer();
    const svgString = serializer.serializeToString(svg);
    return `data:image/svg+xml;base64,${btoa(svgString)}`;
  }

  private applyZeroMarginProcessing(element: HTMLElement): void {
    if (element.tagName.toLowerCase() === 'svg') {
      this.processZeroMarginSVG(element as unknown as SVGElement);
    } else if (element.tagName.toLowerCase() === 'canvas') {
      this.processZeroMarginCanvas(element as HTMLCanvasElement);
    }
  }

  private processZeroMarginSVG(svg: SVGElement): void {
    // Remove margins and padding
    svg.style.margin = '0';
    svg.style.padding = '0';

    // Adjust viewBox to eliminate internal margin
    const viewBox = svg.getAttribute('viewBox');
    if (viewBox) {
      const parts = viewBox.split(' ');
      if (parts.length === 4) {
        const margin = 5; // Estimated internal margin
        parts[0] = (parseFloat(parts[0] || '0') + margin).toString();
        parts[1] = (parseFloat(parts[1] || '0') + margin).toString();
        parts[2] = (parseFloat(parts[2] || '0') - margin * 2).toString();
        parts[3] = (parseFloat(parts[3] || '0') - margin * 2).toString();
        svg.setAttribute('viewBox', parts.join(' '));
      }
    }
  }

  private processZeroMarginCanvas(canvas: HTMLCanvasElement): void {
    canvas.style.margin = '0';
    canvas.style.padding = '0';
  }
}