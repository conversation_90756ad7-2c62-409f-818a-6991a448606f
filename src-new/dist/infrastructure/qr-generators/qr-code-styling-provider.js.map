{"version": 3, "file": "qr-code-styling-provider.js", "sourceRoot": "", "sources": ["../../../infrastructure/qr-generators/qr-code-styling-provider.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,+DAAmE;AAanE,MAAa,qBAAqB;IACxB,MAAM,CAAC,kBAAkB;QAC/B,OAAO,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,WAAW,CAAC;IACtF,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA4B;QAC/C,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,EAAE,EAAE,CAAC;YAChD,MAAM,IAAI,8BAAiB,CAAC,wCAAwC,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEjD,mBAAmB;YACnB,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE7C,mBAAmB;YACnB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YAEzB,qBAAqB;YACrB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE9B,4BAA4B;YAC5B,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAgC,CAAC;YAC7D,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,sDAAsD;YACtD,MAAM,aAAa,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAgB,CAAC;YAE/D,WAAW;YACX,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAEjC,MAAM,MAAM,GAAwB;gBAClC,OAAO,EAAE,aAAa;aACvB,CAAC;YAEF,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YACzD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;YAC3B,CAAC;YAED,OAAO,MAAM,CAAC;QAEhB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,8BAAiB,CACzB,oCAAoC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAC/F,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,OAA4B;QACzD,MAAM,eAAe,GAAG,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE1D,oCAAoC;QACpC,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE/C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,IAAY;QACvC,MAAM,eAAe,GAAwB;YAC3C,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,IAAI,EAAE,KAAK;YACX,IAAI;YACJ,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;gBACT,UAAU,EAAE,CAAC;gBACb,oBAAoB,EAAE,GAAG;aAC1B;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,SAAS;gBAChB,KAAK,EAAE,CAAC;aACT;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,SAAS;aACjB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,SAAS;aACjB;SACF,CAAC;QAEF,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,wBAAwB,CAC5B,IAAY,EACZ,aAAqC,EACrC,SAAgB;QAEhB,MAAM,OAAO,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAEpE,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,OAA4B,EAC5B,SAAe;QAEf,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;YAEhC,MAAM,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC1B,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG;wBACnB,WAAW,EAAE,WAAW;wBACxB,MAAM,EAAE,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC;wBACzC,SAAS,EAAE,OAAO,CAAC,YAAY,EAAE,SAAS,IAAI,GAAG;wBACjD,kBAAkB,EAAE,OAAO,CAAC,YAAY,EAAE,kBAAkB,IAAI,KAAK;qBACtE,CAAC;oBAEF,MAAM,eAAe,GAAG;wBACtB,GAAG,OAAO;wBACV,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,MAAgB;wBACjC,YAAY;qBACb,CAAC;oBAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;oBAC1D,OAAO,CAAC,MAAM,CAAC,CAAC;gBAClB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC;YACH,CAAC,CAAC;YAEF,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACtE,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,IAAY,EAAE,aAAqC;QACnF,uEAAuE;QACvE,MAAM,cAAc,GAAG,GAAG,CAAC;QAC3B,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC;QACxC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;QAEzD,OAAO;YACL,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,WAAW;YACnB,IAAI,EAAE,QAAQ;YACd,IAAI;YACJ,MAAM,EAAE,EAAE,EAAE,8BAA8B;YAC1C,SAAS,EAAE;gBACT,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,SAAS;gBACf,oBAAoB,EAAE,GAAG;aAC1B;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,aAAa,CAAC,SAAS;gBAC9B,IAAI,EAAE,aAAa,CAAC,QAAQ;gBAC5B,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,aAAa,CAAC,eAAe;gBACpC,KAAK,EAAE,CAAC;aACT;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,aAAa,CAAC,iBAAiB;gBACtC,IAAI,EAAE,aAAa,CAAC,gBAAgB;aACrC;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,aAAa,CAAC,cAAc;gBACnC,IAAI,EAAE,aAAa,CAAC,aAAa;aAClC;YACD,YAAY,EAAE;gBACZ,WAAW,EAAE,WAAW;gBACxB,MAAM,EAAE,aAAa,CAAC,WAAW;gBACjC,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;gBACpD,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,IAAY,EACZ,aAAqC,EACrC,SAAgB;QAEhB,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QAE/D,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAEO,oBAAoB,CAAC,IAAY,EAAE,aAAqC;QAC9E,OAAO;YACL,KAAK,EAAE,aAAa,CAAC,MAAM;YAC3B,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,IAAI,EAAE,QAAQ;YACd,IAAI;YACJ,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;gBACT,UAAU,EAAE,CAAC;gBACb,IAAI,EAAE,SAAS;gBACf,oBAAoB,EAAE,GAAG;aAC1B;YACD,WAAW,EAAE;gBACX,KAAK,EAAE,aAAa,CAAC,SAAS;gBAC9B,IAAI,EAAE,aAAa,CAAC,QAAQ;gBAC5B,SAAS,EAAE,IAAI;aAChB;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,aAAa,CAAC,eAAe;gBACpC,KAAK,EAAE,CAAC;aACT;YACD,oBAAoB,EAAE;gBACpB,KAAK,EAAE,aAAa,CAAC,iBAAiB;gBACtC,IAAI,EAAE,aAAa,CAAC,gBAAgB;aACrC;YACD,iBAAiB,EAAE;gBACjB,KAAK,EAAE,aAAa,CAAC,cAAc;gBACnC,IAAI,EAAE,aAAa,CAAC,aAAa;aAClC;YACD,YAAY,EAAE;gBACZ,WAAW,EAAE,WAAW;gBACxB,MAAM,EAAE,aAAa,CAAC,WAAW;gBACjC,SAAS,EAAE,aAAa,CAAC,SAAS;gBAClC,kBAAkB,EAAE,aAAa,CAAC,kBAAkB;gBACpD,UAAU,EAAE,IAAI;aACjB;SACF,CAAC;IACJ,CAAC;IAEO,mBAAmB;QACzB,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAChD,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;QACtC,SAAS,CAAC,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC;QACjC,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC;QAChC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACrC,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEO,gBAAgB,CAAC,SAAsB;QAC7C,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,OAAoB;QAC/C,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,OAA4B,CAAC;YAC5C,OAAO,MAAM,CAAC,SAAS,EAAE,CAAC;QAC5B,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,OAAgC,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,GAAe;QAClC,MAAM,UAAU,GAAG,IAAI,aAAa,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,UAAU,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO,6BAA6B,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;IACxD,CAAC;IAEO,yBAAyB,CAAC,OAAoB;QACpD,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAC5C,IAAI,CAAC,oBAAoB,CAAC,OAAgC,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE,CAAC;YACtD,IAAI,CAAC,uBAAuB,CAAC,OAA4B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,GAAe;QAC1C,6BAA6B;QAC7B,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QACvB,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAExB,8CAA8C;QAC9C,MAAM,OAAO,GAAG,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC5C,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,4BAA4B;gBAC9C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC7D,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAC7D,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACjE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;gBACjE,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,MAAyB;QACvD,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QAC1B,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;IAC7B,CAAC;CACF;AApTD,sDAoTC"}