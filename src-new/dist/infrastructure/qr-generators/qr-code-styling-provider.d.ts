/**
 * QR Code Styling Provider
 * Implementation using the qr-code-styling library
 */
import { QRGenerationOptions, QRCustomizationOptions } from '../../shared/types/pix-types';
declare global {
    interface Window {
        QRCodeStyling: any;
    }
}
export interface QRCodeStylingResult {
    element: HTMLElement;
    dataUrl?: string;
}
export declare class QRCodeStylingProvider {
    private static isLibraryAvailable;
    generateQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult>;
    generateZeroMarginQRCode(options: QRGenerationOptions): Promise<QRCodeStylingResult>;
    generateStandardQRCode(data: string): Promise<QRCodeStylingResult>;
    generateCustomizedQRCode(data: string, customization: QRCustomizationOptions, imageFile?: File): Promise<QRCodeStylingResult>;
    generateWithCustomImage(options: QRGenerationOptions, imageFile: File): Promise<QRCodeStylingResult>;
    private buildCustomizationOptions;
    generateForDownload(data: string, customization: QRCustomizationOptions, imageFile?: File): Promise<QRCodeStylingResult>;
    private buildDownloadOptions;
    private createTempContainer;
    private waitForRendering;
    private cleanupContainer;
    private extractDataUrl;
    private svgToDataUrl;
    private applyZeroMarginProcessing;
    private processZeroMarginSVG;
    private processZeroMarginCanvas;
}
//# sourceMappingURL=qr-code-styling-provider.d.ts.map