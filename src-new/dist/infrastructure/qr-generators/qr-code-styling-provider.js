"use strict";
/**
 * QR Code Styling Provider
 * Implementation using the qr-code-styling library
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRCodeStylingProvider = void 0;
const pix_errors_1 = require("../../shared/errors/pix-errors");
class QRCodeStylingProvider {
    static isLibraryAvailable() {
        return typeof window !== 'undefined' && typeof window.QRCodeStyling !== 'undefined';
    }
    async generateQRCode(options) {
        if (!QRCodeStylingProvider.isLibraryAvailable()) {
            throw new pix_errors_1.QRGenerationError('QRCodeStyling library is not available');
        }
        try {
            const qrCode = new window.QRCodeStyling(options);
            // Create container
            const container = this.createTempContainer();
            // Generate QR code
            qrCode.append(container);
            // Wait for rendering
            await this.waitForRendering();
            // Get the generated element
            const qrElement = container.firstElementChild;
            if (!qrElement) {
                throw new Error('Failed to generate QR code element');
            }
            // Clone element to avoid issues with DOM manipulation
            const clonedElement = qrElement.cloneNode(true);
            // Clean up
            this.cleanupContainer(container);
            const result = {
                element: clonedElement
            };
            const dataUrl = await this.extractDataUrl(clonedElement);
            if (dataUrl) {
                result.dataUrl = dataUrl;
            }
            return result;
        }
        catch (error) {
            throw new pix_errors_1.QRGenerationError(`QRCodeStyling generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async generateZeroMarginQRCode(options) {
        const modifiedOptions = { ...options, margin: 0 };
        const result = await this.generateQRCode(modifiedOptions);
        // Apply zero margin post-processing
        this.applyZeroMarginProcessing(result.element);
        return result;
    }
    async generateStandardQRCode(data) {
        const standardOptions = {
            width: 300,
            height: 300,
            type: 'svg',
            data,
            margin: 10,
            qrOptions: {
                typeNumber: 0,
                errorCorrectionLevel: 'M'
            },
            dotsOptions: {
                color: '#000000',
                type: 'square',
                roundSize: true
            },
            backgroundOptions: {
                color: '#ffffff',
                round: 0
            },
            cornersSquareOptions: {
                color: '#000000'
            },
            cornersDotOptions: {
                color: '#000000'
            }
        };
        return this.generateQRCode(standardOptions);
    }
    async generateCustomizedQRCode(data, customization, imageFile) {
        const options = this.buildCustomizationOptions(data, customization);
        if (imageFile) {
            return this.generateWithCustomImage(options, imageFile);
        }
        return this.generateQRCode(options);
    }
    async generateWithCustomImage(options, imageFile) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const imageOptions = {
                        crossOrigin: 'anonymous',
                        margin: options.imageOptions?.margin || 0,
                        imageSize: options.imageOptions?.imageSize || 0.4,
                        hideBackgroundDots: options.imageOptions?.hideBackgroundDots || false
                    };
                    const modifiedOptions = {
                        ...options,
                        image: e.target?.result,
                        imageOptions
                    };
                    const result = await this.generateQRCode(modifiedOptions);
                    resolve(result);
                }
                catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => reject(new Error('Failed to read image file'));
            reader.readAsDataURL(imageFile);
        });
    }
    buildCustomizationOptions(data, customization) {
        // Create display options with constrained size (max 400px for display)
        const maxDisplaySize = 400;
        const exportSize = customization.qrSize;
        const displaySize = Math.min(exportSize, maxDisplaySize);
        return {
            width: displaySize,
            height: displaySize,
            type: 'canvas',
            data,
            margin: 10, // Standard margin for display
            qrOptions: {
                typeNumber: 0,
                mode: undefined,
                errorCorrectionLevel: 'M'
            },
            dotsOptions: {
                color: customization.dotsColor,
                type: customization.dotsType,
                roundSize: true
            },
            backgroundOptions: {
                color: customization.backgroundColor,
                round: 0
            },
            cornersSquareOptions: {
                color: customization.cornerSquareColor,
                type: customization.cornerSquareType
            },
            cornersDotOptions: {
                color: customization.cornerDotColor,
                type: customization.cornerDotType
            },
            imageOptions: {
                crossOrigin: 'anonymous',
                margin: customization.imageMargin,
                imageSize: customization.imageSize,
                hideBackgroundDots: customization.hideBackgroundDots,
                saveAsBlob: true
            }
        };
    }
    async generateForDownload(data, customization, imageFile) {
        const options = this.buildDownloadOptions(data, customization);
        if (imageFile) {
            return this.generateWithCustomImage(options, imageFile);
        }
        return this.generateQRCode(options);
    }
    buildDownloadOptions(data, customization) {
        return {
            width: customization.qrSize,
            height: customization.qrSize,
            type: 'canvas',
            data,
            margin: 10,
            qrOptions: {
                typeNumber: 0,
                mode: undefined,
                errorCorrectionLevel: 'M'
            },
            dotsOptions: {
                color: customization.dotsColor,
                type: customization.dotsType,
                roundSize: true
            },
            backgroundOptions: {
                color: customization.backgroundColor,
                round: 0
            },
            cornersSquareOptions: {
                color: customization.cornerSquareColor,
                type: customization.cornerSquareType
            },
            cornersDotOptions: {
                color: customization.cornerDotColor,
                type: customization.cornerDotType
            },
            imageOptions: {
                crossOrigin: 'anonymous',
                margin: customization.imageMargin,
                imageSize: customization.imageSize,
                hideBackgroundDots: customization.hideBackgroundDots,
                saveAsBlob: true
            }
        };
    }
    createTempContainer() {
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '-9999px';
        document.body.appendChild(container);
        return container;
    }
    async waitForRendering() {
        return new Promise(resolve => setTimeout(resolve, 100));
    }
    cleanupContainer(container) {
        if (container.parentNode) {
            container.parentNode.removeChild(container);
        }
    }
    async extractDataUrl(element) {
        if (element.tagName.toLowerCase() === 'canvas') {
            const canvas = element;
            return canvas.toDataURL();
        }
        if (element.tagName.toLowerCase() === 'svg') {
            return this.svgToDataUrl(element);
        }
        return undefined;
    }
    svgToDataUrl(svg) {
        const serializer = new XMLSerializer();
        const svgString = serializer.serializeToString(svg);
        return `data:image/svg+xml;base64,${btoa(svgString)}`;
    }
    applyZeroMarginProcessing(element) {
        if (element.tagName.toLowerCase() === 'svg') {
            this.processZeroMarginSVG(element);
        }
        else if (element.tagName.toLowerCase() === 'canvas') {
            this.processZeroMarginCanvas(element);
        }
    }
    processZeroMarginSVG(svg) {
        // Remove margins and padding
        svg.style.margin = '0';
        svg.style.padding = '0';
        // Adjust viewBox to eliminate internal margin
        const viewBox = svg.getAttribute('viewBox');
        if (viewBox) {
            const parts = viewBox.split(' ');
            if (parts.length === 4) {
                const margin = 5; // Estimated internal margin
                parts[0] = (parseFloat(parts[0] || '0') + margin).toString();
                parts[1] = (parseFloat(parts[1] || '0') + margin).toString();
                parts[2] = (parseFloat(parts[2] || '0') - margin * 2).toString();
                parts[3] = (parseFloat(parts[3] || '0') - margin * 2).toString();
                svg.setAttribute('viewBox', parts.join(' '));
            }
        }
    }
    processZeroMarginCanvas(canvas) {
        canvas.style.margin = '0';
        canvas.style.padding = '0';
    }
}
exports.QRCodeStylingProvider = QRCodeStylingProvider;
//# sourceMappingURL=qr-code-styling-provider.js.map