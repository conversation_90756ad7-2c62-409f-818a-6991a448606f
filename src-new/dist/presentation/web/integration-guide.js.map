{"version": 3, "file": "integration-guide.js", "sourceRoot": "", "sources": ["../../../presentation/web/integration-guide.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAQH,gDAsDC;AA0BD,sEAuCC;AAKD,wDA2BC;AA7JD,qDAAwE;AAExE;;;GAGG;AACH,SAAgB,kBAAkB;IAChC,mDAAmD;IACnD,MAAM,QAAQ,GAA0B;QACtC,gBAAgB;QAChB,IAAI,EAAE,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAoB;QAC3D,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAsB;QACtE,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAqB;QAClE,iBAAiB,EAAE,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAqB;QAC9E,iBAAiB,EAAE,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAqB;QAC9E,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAqB;QAClE,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAqB;QACxE,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAwB;QAC/E,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB;QACxE,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAgB;QAEtE,yBAAyB;QACzB,mBAAmB,EAAE,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAsB;QACxF,kBAAkB,EAAE,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAgB;QAChF,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAsB;QACnE,SAAS,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB;QACpE,gBAAgB,EAAE,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAAsB;QACpF,iBAAiB,EAAE,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAAqB;QACrF,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAsB;QAC9E,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAqB;QAC/E,eAAe,EAAE,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAqB;QAChF,MAAM,EAAE,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAsB;QAC/D,SAAS,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAqB;QACpE,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAgB;QAC1E,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAqB;QACxE,kBAAkB,EAAE,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAAqB;QACvF,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAqB;QAExE,mBAAmB;QACnB,aAAa,EAAE,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAgB;QACtE,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAgB;QAC5D,SAAS,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAgB;QAC/D,UAAU,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAgB;QAChE,UAAU,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAgB;QAChE,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB;QACxE,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB;QAC9E,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAsB;QAChE,cAAc,EAAE,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAgB;QAExE,iBAAiB;QACjB,UAAU,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAgB;QAChE,YAAY,EAAE,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAgB;QACpE,UAAU,EAAE,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAsB;KACvE,CAAC;IAEF,uCAAuC;IACvC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAE3B,2CAA2C;IAC3C,OAAO,IAAI,8BAAa,CAAC,QAAQ,CAAC,CAAC;AACrC,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,QAA+B;IACvD,MAAM,eAAe,GAAa,EAAE,CAAC;IAErC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,EAAE;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CACb,mCAAmC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACjE,sEAAsE,CACvE,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAgB,6BAA6B;IAC3C,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;QACjD,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,kBAAkB,EAAE,CAAC;YAE3C,kCAAkC;YACjC,MAAc,CAAC,YAAY,GAAG,aAAa,CAAC;YAC5C,MAAc,CAAC,WAAW,GAAG,CAAC,UAAkB,EAAE,EAAE;gBACnD,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC,CAAC;QACrF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YAEjE,mCAAmC;YACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC/C,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;;;;;OAUxB,CAAC;YACF,QAAQ,CAAC,SAAS,GAAG;;;OAGpB,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YAEpC,UAAU,CAAC,GAAG,EAAE;gBACd,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACtC,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB;IAKpC,MAAM,WAAW,GAAG;QAClB,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc;QAC9D,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe;QACpE,qBAAqB,EAAE,oBAAoB,EAAE,WAAW,EAAE,YAAY;QACtE,oBAAoB,EAAE,qBAAqB,EAAE,iBAAiB;QAC9D,kBAAkB,EAAE,kBAAkB,EAAE,SAAS,EAAE,YAAY;QAC/D,kBAAkB,EAAE,cAAc,EAAE,sBAAsB;QAC1D,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,YAAY;QACzD,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,gBAAgB;QAC3D,SAAS,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,YAAY;KACxE,CAAC;IAEF,MAAM,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;IACvE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CACnC,qBAAqB,EAAE,iBAAiB,CACzC,CAAC;IAEF,OAAO;QACL,UAAU,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC;QAChC,OAAO;QACP,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,oEAAoE;AACpE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;IAClC,6BAA6B,EAAE,CAAC;AAClC,CAAC"}