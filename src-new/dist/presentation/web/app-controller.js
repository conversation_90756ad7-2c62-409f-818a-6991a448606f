"use strict";
/**
 * Main Application Controller
 * Orchestrates all UI components and coordinates with the application layer
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
exports.initializeApp = initializeApp;
const pix_key_1 = require("../../domain/value-objects/pix-key");
const money_1 = require("../../domain/value-objects/money");
const pix_transaction_1 = require("../../domain/entities/pix-transaction");
const generate_pix_qrcode_1 = require("../../application/usecases/generate-pix-qrcode");
const qr_code_repository_enhanced_1 = require("../../infrastructure/repositories/qr-code-repository-enhanced");
const pix_form_component_1 = require("./components/pix-form-component");
const qr_customization_component_1 = require("./components/qr-customization-component");
const qr_display_component_1 = require("./components/qr-display-component");
const pix_errors_1 = require("../../shared/errors/pix-errors");
class AppController {
    constructor(elements) {
        this.currentFormData = null;
        this.currentCustomization = null;
        this.elements = elements;
        // Initialize infrastructure
        this.qrRepository = new qr_code_repository_enhanced_1.QRCodeRepositoryEnhanced();
        this.generatePixQRCodeUseCase = new generate_pix_qrcode_1.GeneratePixQRCodeUseCase(this.qrRepository);
        // Initialize components
        this.initializeComponents();
        this.setupGlobalEventListeners();
    }
    initializeComponents() {
        // PIX Form Component
        const formCallbacks = {
            onFormSubmit: (data) => this.handleFormSubmit(data),
            onKeyValidation: (isValid, message) => this.handleKeyValidation(isValid, message),
            onFormChange: (data) => this.handleFormChange(data)
        };
        this.pixFormComponent = new pix_form_component_1.PixFormComponent(this.elements, formCallbacks);
        // QR Customization Component
        const customizationCallbacks = {
            onCustomizationChange: (options) => this.handleCustomizationChange(options),
            onToggleCustomization: (active) => this.handleToggleCustomization(active),
            onImageUpload: (file) => this.handleImageUpload(file)
        };
        this.qrCustomizationComponent = new qr_customization_component_1.QRCustomizationComponent(this.elements, customizationCallbacks);
        // QR Display Component
        const displayCallbacks = {
            onCopyBRCode: (brCode) => this.copyBRCode(brCode),
            onDownloadRequest: (format) => this.downloadQRCode(format),
            showToast: (message, type) => this.showToast(message, type)
        };
        this.qrDisplayComponent = new qr_display_component_1.QRDisplayComponent(this.elements, displayCallbacks);
    }
    setupGlobalEventListeners() {
        // Modal close
        this.elements.closeModal.addEventListener('click', () => this.hideModal());
        // Modal backdrop click
        this.elements.errorModal.addEventListener('click', (e) => {
            if (e.target === this.elements.errorModal) {
                this.hideModal();
            }
        });
        // ESC key to close modal
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.elements.errorModal.style.display === 'block') {
                this.hideModal();
            }
        });
    }
    async handleFormSubmit(data) {
        try {
            this.setLoading(true);
            this.currentFormData = data;
            // Create domain objects with validation
            const pixKey = pix_key_1.PixKey.create(data.pixKey, data.keyType);
            const amount = money_1.Money.create(data.amount);
            const transactionParams = {
                pixKey,
                receiverName: data.receiverName,
                receiverCity: data.receiverCity,
                amount
            };
            if (data.reference) {
                transactionParams.reference = data.reference;
            }
            if (data.description) {
                transactionParams.description = data.description;
            }
            const transaction = pix_transaction_1.PixTransaction.create(transactionParams);
            // Generate QR Code
            const useCaseRequest = {
                transaction,
                isDynamic: false
            };
            if (this.currentCustomization) {
                useCaseRequest.customization = this.currentCustomization;
            }
            const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);
            // Display result
            const displayData = {
                brCode: result.brCode,
                transaction: result.transaction
            };
            if (result.qrCodeDataUrl) {
                displayData.qrCodeDataUrl = result.qrCodeDataUrl;
            }
            this.qrDisplayComponent.displayQRResult(displayData);
            this.showToast('QR Code PIX gerado com sucesso!', 'success');
        }
        catch (error) {
            console.error('Error generating QR Code:', error);
            this.handleError(error);
        }
        finally {
            this.setLoading(false);
        }
    }
    handleFormChange(data) {
        // Update current form data
        this.currentFormData = { ...this.currentFormData, ...data };
        // If QR is already displayed and form is valid, regenerate
        if (this.qrDisplayComponent.getCurrentBRCode() && this.isFormComplete(data)) {
            this.regenerateQRIfNeeded();
        }
    }
    handleKeyValidation(isValid, message) {
        // Key validation feedback is handled by the form component
        // Could add additional logic here if needed
    }
    handleCustomizationChange(options) {
        this.currentCustomization = options;
        this.regenerateQRIfNeeded();
    }
    handleToggleCustomization(active) {
        if (!active) {
            this.currentCustomization = null;
        }
        this.regenerateQRIfNeeded();
    }
    handleImageUpload(file) {
        // Image upload is handled within customization
        // Regeneration will be triggered by customization change
    }
    handleMarginChange(margin) {
        this.qrDisplayComponent.updateMarginStyles(margin);
        this.regenerateQRIfNeeded();
    }
    async regenerateQRIfNeeded() {
        if (!this.currentFormData || !this.isFormComplete(this.currentFormData)) {
            return;
        }
        try {
            // Create transaction from current form data
            const pixKey = pix_key_1.PixKey.create(this.currentFormData.pixKey, this.currentFormData.keyType);
            const amount = money_1.Money.create(this.currentFormData.amount);
            const transactionParams = {
                pixKey,
                receiverName: this.currentFormData.receiverName,
                receiverCity: this.currentFormData.receiverCity,
                amount
            };
            if (this.currentFormData.reference) {
                transactionParams.reference = this.currentFormData.reference;
            }
            if (this.currentFormData.description) {
                transactionParams.description = this.currentFormData.description;
            }
            const transaction = pix_transaction_1.PixTransaction.create(transactionParams);
            // Regenerate QR Code
            const useCaseRequest = {
                transaction,
                isDynamic: false
            };
            if (this.currentCustomization) {
                useCaseRequest.customization = this.currentCustomization;
            }
            const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);
            // Update display
            const displayData = {
                brCode: result.brCode,
                transaction: result.transaction
            };
            if (result.qrCodeDataUrl) {
                displayData.qrCodeDataUrl = result.qrCodeDataUrl;
            }
            this.qrDisplayComponent.displayQRResult(displayData);
        }
        catch (error) {
            console.error('Error regenerating QR Code:', error);
            // Don't show error toast for regeneration failures
        }
    }
    async copyBRCode(brCode) {
        try {
            await navigator.clipboard.writeText(brCode);
        }
        catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = brCode;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }
    async downloadQRCode(format) {
        const brCode = this.qrDisplayComponent.getCurrentBRCode();
        if (!brCode) {
            throw new Error('No QR Code to download');
        }
        const filename = this.generateFilename(format);
        await this.qrDisplayComponent.downloadCurrentQR({
            filename,
            format,
            quality: 0.9
        });
    }
    generateFilename(format) {
        const isCustomized = this.qrCustomizationComponent.isCustomizationActive();
        const baseName = isCustomized ? 'qrcode-pix-personalizado' : 'qrcode-pix-padrao';
        return `${baseName}.${format}`;
    }
    handleError(error) {
        let message = 'Erro interno do servidor';
        if (error instanceof pix_errors_1.ValidationError) {
            message = error.message;
        }
        else if (error instanceof pix_errors_1.PixError) {
            message = error.message;
        }
        else if (error instanceof Error) {
            message = `Erro ao gerar QR Code: ${error.message}`;
        }
        this.showError(message);
    }
    isFormComplete(data) {
        return !!(data.keyType &&
            data.pixKey?.trim() &&
            data.receiverName?.trim() &&
            data.receiverCity?.trim());
    }
    setLoading(loading) {
        this.pixFormComponent.setLoading(loading);
        this.qrDisplayComponent.setLoading(loading);
    }
    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.errorModal.style.display = 'block';
    }
    hideModal() {
        this.elements.errorModal.style.display = 'none';
    }
    showToast(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        // Hide toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
    // Public API for external control
    applyPreset(presetName) {
        this.qrCustomizationComponent.applyPreset(presetName);
    }
    reset() {
        this.pixFormComponent.reset();
        this.qrCustomizationComponent.reset();
        this.qrDisplayComponent.hideResult();
        this.currentFormData = null;
        this.currentCustomization = null;
    }
    getCurrentFormData() {
        return this.currentFormData;
    }
    getCurrentCustomization() {
        return this.currentCustomization;
    }
}
exports.AppController = AppController;
function initializeApp(elements) {
    const app = new AppController(elements);
    // Set global references for compatibility
    window.pixGenerator = app;
    window.applyPreset = (presetName) => app.applyPreset(presetName);
    return app;
}
//# sourceMappingURL=app-controller.js.map