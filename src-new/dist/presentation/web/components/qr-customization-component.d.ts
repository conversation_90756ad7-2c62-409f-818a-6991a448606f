/**
 * QR Customization Component
 * Manages QR code styling and customization options
 */
import { QRCustomizationOptions } from '../../../shared/types/pix-types';
export interface QRCustomizationElements {
    customizationToggle: HTMLElement;
    customizationPanel: HTMLElement;
    dotsType: HTMLSelectElement;
    dotsColor: HTMLInputElement;
    cornerSquareType: HTMLSelectElement;
    cornerSquareColor: HTMLInputElement;
    cornerDotType: HTMLSelectElement;
    cornerDotColor: HTMLInputElement;
    backgroundColor: HTMLInputElement;
    qrSize: HTMLSelectElement;
    imageSize: HTMLInputElement;
    imageSizeValue: HTMLElement;
    imageMargin: HTMLInputElement;
    hideBackgroundDots: HTMLInputElement;
    centerImage: HTMLInputElement;
}
export interface QRCustomizationCallbacks {
    onCustomizationChange: (options: QRCustomizationOptions) => void;
    onToggleCustomization: (active: boolean) => void;
    onImageUpload: (file: File) => void;
}
export declare class QRCustomizationComponent {
    private elements;
    private callbacks;
    private isActive;
    private currentImageFile;
    constructor(elements: QRCustomizationElements, callbacks: QRCustomizationCallbacks);
    private setupEventListeners;
    private initializeValues;
    private toggleCustomization;
    private handleCustomizationChange;
    private handleImageUpload;
    private handleSizeChange;
    private updateRangeValue;
    private getCurrentCustomization;
    applyPreset(presetName: string): void;
    private applyPresetValues;
    getCurrentMargin(): number;
    isCustomizationActive(): boolean;
    getCurrentImageFile(): File | null;
    reset(): void;
}
//# sourceMappingURL=qr-customization-component.d.ts.map