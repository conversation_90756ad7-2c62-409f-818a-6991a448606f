"use strict";
/**
 * QR Customization Component
 * Manages QR code styling and customization options
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRCustomizationComponent = void 0;
class QRCustomizationComponent {
    constructor(elements, callbacks) {
        this.isActive = false;
        this.currentImageFile = null;
        this.elements = elements;
        this.callbacks = callbacks;
        this.setupEventListeners();
        this.initializeValues();
    }
    setupEventListeners() {
        // Toggle switch
        this.elements.customizationToggle.addEventListener('click', () => {
            this.toggleCustomization();
        });
        // Customization controls
        const customizationInputs = [
            'dotsType', 'dotsColor', 'cornerSquareType', 'cornerSquareColor',
            'cornerDotType', 'cornerDotColor', 'backgroundColor', 'qrSize',
            'imageSize', 'imageMargin', 'hideBackgroundDots'
        ];
        customizationInputs.forEach((id) => {
            const element = this.elements[id];
            if (element) {
                if (element.type === 'range') {
                    element.addEventListener('input', () => this.updateRangeValue(id));
                }
                if (id === 'qrSize') {
                    element.addEventListener('change', (e) => this.handleSizeChange(e));
                }
                element.addEventListener('change', () => this.handleCustomizationChange());
                element.addEventListener('input', () => this.handleCustomizationChange());
            }
        });
        // Image upload
        this.elements.centerImage.addEventListener('change', (e) => {
            this.handleImageUpload(e);
        });
        // Initialize range values
        this.updateRangeValue('imageSize');
    }
    initializeValues() {
        // Set default values and update displays
        this.updateRangeValue('imageSize');
    }
    toggleCustomization() {
        this.isActive = !this.isActive;
        this.elements.customizationToggle.classList.toggle('active', this.isActive);
        this.elements.customizationPanel.classList.toggle('active', this.isActive);
        this.callbacks.onToggleCustomization(this.isActive);
        if (this.isActive) {
            this.handleCustomizationChange();
        }
    }
    handleCustomizationChange() {
        if (!this.isActive)
            return;
        const options = this.getCurrentCustomization();
        this.callbacks.onCustomizationChange(options);
    }
    handleImageUpload(event) {
        const target = event.target;
        const file = target.files?.[0];
        if (file) {
            this.currentImageFile = file;
            this.callbacks.onImageUpload(file);
            // Update label text
            const label = document.querySelector('.image-upload-label');
            if (label) {
                label.innerHTML = `✅ ${file.name}<br><small>Clique para alterar</small>`;
            }
            this.handleCustomizationChange();
        }
    }
    handleSizeChange(event) {
        const target = event.target;
        const size = parseInt(target.value);
        // Update hidden width/height fields for compatibility
        const widthField = document.getElementById('qrWidth');
        const heightField = document.getElementById('qrHeight');
        if (widthField)
            widthField.value = size.toString();
        if (heightField)
            heightField.value = size.toString();
        this.handleCustomizationChange();
    }
    updateRangeValue(inputId) {
        const input = this.elements[inputId];
        if (!input)
            return;
        let valueElement = null;
        let displayValue = '';
        switch (inputId) {
            case 'imageSize':
                valueElement = this.elements.imageSizeValue;
                displayValue = Math.round(parseFloat(input.value) * 100) + '%';
                break;
        }
        if (valueElement) {
            valueElement.textContent = displayValue;
        }
    }
    getCurrentCustomization() {
        return {
            dotsType: this.elements.dotsType.value,
            dotsColor: this.elements.dotsColor.value,
            cornerSquareType: this.elements.cornerSquareType.value || undefined,
            cornerSquareColor: this.elements.cornerSquareColor.value,
            cornerDotType: this.elements.cornerDotType.value || undefined,
            cornerDotColor: this.elements.cornerDotColor.value,
            backgroundColor: this.elements.backgroundColor.value,
            qrSize: parseInt(this.elements.qrSize.value) || 300,
            imageSize: parseFloat(this.elements.imageSize.value) || 0.4,
            imageMargin: parseInt(this.elements.imageMargin.value) || 0,
            hideBackgroundDots: this.elements.hideBackgroundDots.checked
        };
    }
    // Public methods
    applyPreset(presetName) {
        const presets = {
            modern: {
                dotsType: 'rounded',
                dotsColor: '#667eea',
                cornerSquareType: 'extra-rounded',
                cornerSquareColor: '#764ba2',
                cornerDotType: 'rounded',
                cornerDotColor: '#667eea',
                backgroundColor: '#ffffff'
            },
            classic: {
                dotsType: 'square',
                dotsColor: '#000000',
                cornerSquareType: 'square',
                cornerSquareColor: '#000000',
                cornerDotType: 'square',
                cornerDotColor: '#000000',
                backgroundColor: '#ffffff'
            },
            elegant: {
                dotsType: 'classy-rounded',
                dotsColor: '#2c3e50',
                cornerSquareType: 'rounded',
                cornerSquareColor: '#34495e',
                cornerDotType: 'rounded',
                cornerDotColor: '#2c3e50',
                backgroundColor: '#ecf0f1'
            },
            vibrant: {
                dotsType: 'dots',
                dotsColor: '#e74c3c',
                cornerSquareType: 'extra-rounded',
                cornerSquareColor: '#3498db',
                cornerDotType: 'dot',
                cornerDotColor: '#f39c12',
                backgroundColor: '#ffffff'
            },
            circular: {
                dotsType: 'rounded',
                dotsColor: '#2c3e50',
                cornerSquareType: 'dot',
                cornerSquareColor: '#e74c3c',
                cornerDotType: 'dot',
                cornerDotColor: '#3498db',
                backgroundColor: '#ffffff'
            }
        };
        const preset = presets[presetName];
        if (preset) {
            this.applyPresetValues(preset);
            this.handleCustomizationChange();
        }
    }
    applyPresetValues(preset) {
        if (preset.dotsType)
            this.elements.dotsType.value = preset.dotsType;
        if (preset.dotsColor)
            this.elements.dotsColor.value = preset.dotsColor;
        if (preset.cornerSquareType)
            this.elements.cornerSquareType.value = preset.cornerSquareType;
        if (preset.cornerSquareColor)
            this.elements.cornerSquareColor.value = preset.cornerSquareColor;
        if (preset.cornerDotType)
            this.elements.cornerDotType.value = preset.cornerDotType;
        if (preset.cornerDotColor)
            this.elements.cornerDotColor.value = preset.cornerDotColor;
        if (preset.backgroundColor)
            this.elements.backgroundColor.value = preset.backgroundColor;
    }
    getCurrentMargin() {
        return parseInt(this.elements.imageMargin.value) || 0;
    }
    isCustomizationActive() {
        return this.isActive;
    }
    getCurrentImageFile() {
        return this.currentImageFile;
    }
    reset() {
        this.isActive = false;
        this.elements.customizationToggle.classList.remove('active');
        this.elements.customizationPanel.classList.remove('active');
        this.currentImageFile = null;
        // Reset to default values
        this.elements.imageMargin.value = '0';
        // Reset image label
        const label = document.querySelector('.image-upload-label');
        if (label) {
            label.innerHTML = 'Escolher imagem<br><small>PNG, JPG até 5MB</small>';
        }
    }
}
exports.QRCustomizationComponent = QRCustomizationComponent;
//# sourceMappingURL=qr-customization-component.js.map