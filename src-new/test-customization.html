<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Personalização - QR Code PIX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        #testResults {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste de Personalização - QR Code PIX</h1>
    
    <div class="test-section">
        <h2>📋 Checklist de Funcionalidades</h2>
        <div id="testResults">
            <div class="info">Clique em "Executar Testes" para verificar todas as funcionalidades.</div>
        </div>
        <button onclick="runAllTests()">🚀 Executar Testes</button>
        <button onclick="openMainApp()">🔗 Abrir Aplicação Principal</button>
    </div>

    <div class="test-section">
        <h2>📝 Instruções de Teste Manual</h2>
        <ol>
            <li><strong>Toggle de Personalização:</strong> Clique no toggle para ativar/desativar as opções avançadas</li>
            <li><strong>Presets:</strong> Teste todos os 5 presets (Modern, Clássico, Elegante, Vibrante, Circular)</li>
            <li><strong>Cores:</strong> Altere as cores dos pontos, cantos e fundo</li>
            <li><strong>Tipos de Pontos:</strong> Teste todos os tipos (quadrado, arredondado, círculo, clássico, etc.)</li>
            <li><strong>Position Patterns:</strong> Teste tipos de quadrados e pontos centrais dos cantos</li>
            <li><strong>Imagem Central:</strong> Faça upload de uma imagem e ajuste tamanho/margem</li>
            <li><strong>Dimensões:</strong> Teste diferentes tamanhos de QR Code (100px a 2000px)</li>
            <li><strong>Geração:</strong> Gere um QR Code PIX com dados válidos</li>
            <li><strong>Download:</strong> Teste o download do QR Code em PNG</li>
            <li><strong>Responsividade:</strong> Teste em diferentes tamanhos de tela</li>
        </ol>
    </div>

    <script>
        function runAllTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="info">Executando testes...</div>';
            
            const tests = [
                testPageLoad,
                testCustomizationElements,
                testPresetButtons,
                testColorInputs,
                testSelectElements,
                testRangeInputs,
                testFileInput,
                testToggleFunctionality
            ];
            
            let results = [];
            
            tests.forEach((test, index) => {
                try {
                    const result = test();
                    results.push({
                        name: test.name,
                        status: result.success ? 'success' : 'error',
                        message: result.message
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        status: 'error',
                        message: `Erro: ${error.message}`
                    });
                }
            });
            
            displayResults(results);
        }
        
        function testPageLoad() {
            return {
                success: true,
                message: 'Página carregada com sucesso'
            };
        }
        
        function testCustomizationElements() {
            const elements = [
                'customizationToggle',
                'customizationPanel',
                'dotsType',
                'dotsColor',
                'cornerSquareType',
                'cornerSquareColor',
                'cornerDotType',
                'cornerDotColor',
                'backgroundColor',
                'qrSize',
                'imageSize',
                'imageSizeValue',
                'imageMargin',
                'hideBackgroundDots',
                'centerImage'
            ];
            
            const missing = elements.filter(id => !document.getElementById(id));
            
            if (missing.length === 0) {
                return {
                    success: true,
                    message: `Todos os ${elements.length} elementos de personalização estão presentes`
                };
            } else {
                return {
                    success: false,
                    message: `Elementos faltando: ${missing.join(', ')}`
                };
            }
        }
        
        function testPresetButtons() {
            const presetButtons = document.querySelectorAll('.preset-btn');
            const expectedPresets = ['modern', 'classic', 'elegant', 'vibrant', 'circular'];
            
            if (presetButtons.length === expectedPresets.length) {
                return {
                    success: true,
                    message: `${presetButtons.length} botões de preset encontrados`
                };
            } else {
                return {
                    success: false,
                    message: `Esperado ${expectedPresets.length} presets, encontrado ${presetButtons.length}`
                };
            }
        }
        
        function testColorInputs() {
            const colorInputs = ['dotsColor', 'cornerSquareColor', 'cornerDotColor', 'backgroundColor'];
            const validInputs = colorInputs.filter(id => {
                const element = document.getElementById(id);
                return element && element.type === 'color';
            });
            
            return {
                success: validInputs.length === colorInputs.length,
                message: `${validInputs.length}/${colorInputs.length} inputs de cor válidos`
            };
        }
        
        function testSelectElements() {
            const selects = ['dotsType', 'cornerSquareType', 'cornerDotType', 'qrSize'];
            const validSelects = selects.filter(id => {
                const element = document.getElementById(id);
                return element && element.tagName === 'SELECT';
            });
            
            return {
                success: validSelects.length === selects.length,
                message: `${validSelects.length}/${selects.length} elementos select válidos`
            };
        }
        
        function testRangeInputs() {
            const ranges = ['imageSize'];
            const validRanges = ranges.filter(id => {
                const element = document.getElementById(id);
                return element && element.type === 'range';
            });
            
            return {
                success: validRanges.length === ranges.length,
                message: `${validRanges.length}/${ranges.length} inputs range válidos`
            };
        }
        
        function testFileInput() {
            const fileInput = document.getElementById('centerImage');
            const isValid = fileInput && fileInput.type === 'file' && fileInput.accept === 'image/*';
            
            return {
                success: isValid,
                message: isValid ? 'Input de arquivo configurado corretamente' : 'Input de arquivo inválido'
            };
        }
        
        function testToggleFunctionality() {
            const toggle = document.getElementById('customizationToggle');
            const panel = document.getElementById('customizationPanel');
            
            if (!toggle || !panel) {
                return {
                    success: false,
                    message: 'Toggle ou painel não encontrado'
                };
            }
            
            // Simular clique no toggle
            const initialPanelDisplay = window.getComputedStyle(panel).display;
            
            return {
                success: true,
                message: `Toggle e painel encontrados. Painel inicialmente: ${initialPanelDisplay}`
            };
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('testResults');
            let html = '<h3>📊 Resultados dos Testes:</h3>';
            
            const successCount = results.filter(r => r.status === 'success').length;
            const totalCount = results.length;
            
            html += `<div class="info">✅ ${successCount}/${totalCount} testes passaram</div>`;
            
            results.forEach(result => {
                html += `<div class="${result.status}">
                    <strong>${result.name}:</strong> ${result.message}
                </div>`;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        function openMainApp() {
            window.open('http://localhost:3000', '_blank');
        }
    </script>
</body>
</html>
