/**
 * Main Application Controller
 * Orchestrates all UI components and coordinates with the application layer
 */

import { Pix<PERSON><PERSON> } from '../../domain/value-objects/pix-key';
import { Money } from '../../domain/value-objects/money';
import { PixTransaction } from '../../domain/entities/pix-transaction';
import { GeneratePixQRCodeUseCase } from '../../application/usecases/generate-pix-qrcode';
import { QRCodeRepositoryEnhanced } from '../../infrastructure/repositories/qr-code-repository-enhanced';
import { PixFormComponent, PixFormElements, PixFormCallbacks } from './components/pix-form-component';
import { QRCustomizationComponent, QRCustomizationElements, QRCustomizationCallbacks } from './components/qr-customization-component';
import { QRDisplayComponent, QRDisplayElements, QRDisplayCallbacks } from './components/qr-display-component';
import { ValidationError, PixError } from '../../shared/errors/pix-errors';
import { PixFormData, QRCustomizationOptions } from '../../shared/types/pix-types';

export interface AppControllerElements extends PixFormElements, QRCustomizationElements, QRDisplayElements {
  errorModal: HTMLElement;
  errorMessage: HTMLElement;
  closeModal: HTMLButtonElement;
}

export class AppController {
  private elements: AppControllerElements;
  private pixFormComponent!: PixFormComponent;
  private qrCustomizationComponent!: QRCustomizationComponent;
  private qrDisplayComponent!: QRDisplayComponent;
  private generatePixQRCodeUseCase: GeneratePixQRCodeUseCase;
  private qrRepository: QRCodeRepositoryEnhanced;
  
  private currentFormData: PixFormData | null = null;
  private currentCustomization: QRCustomizationOptions | null = null;

  constructor(elements: AppControllerElements) {
    this.elements = elements;
    
    // Initialize infrastructure
    this.qrRepository = new QRCodeRepositoryEnhanced();
    this.generatePixQRCodeUseCase = new GeneratePixQRCodeUseCase(this.qrRepository);
    
    // Initialize components
    this.initializeComponents();
    this.setupGlobalEventListeners();
  }

  private initializeComponents(): void {
    // PIX Form Component
    const formCallbacks: PixFormCallbacks = {
      onFormSubmit: (data) => this.handleFormSubmit(data),
      onKeyValidation: (isValid, message) => this.handleKeyValidation(isValid, message),
      onFormChange: (data) => this.handleFormChange(data)
    };

    this.pixFormComponent = new PixFormComponent(this.elements, formCallbacks);

    // QR Customization Component
    const customizationCallbacks: QRCustomizationCallbacks = {
      onCustomizationChange: (options) => this.handleCustomizationChange(options),
      onToggleCustomization: (active) => this.handleToggleCustomization(active),
      onImageUpload: (file) => this.handleImageUpload(file)
    };

    this.qrCustomizationComponent = new QRCustomizationComponent(this.elements, customizationCallbacks);

    // QR Display Component
    const displayCallbacks: QRDisplayCallbacks = {
      onCopyBRCode: (brCode) => this.copyBRCode(brCode),
      onDownloadRequest: (format) => this.downloadQRCode(format),
      showToast: (message, type) => this.showToast(message, type)
    };

    this.qrDisplayComponent = new QRDisplayComponent(this.elements, displayCallbacks);
  }

  private setupGlobalEventListeners(): void {
    // Modal close
    this.elements.closeModal.addEventListener('click', () => this.hideModal());

    // Modal backdrop click
    this.elements.errorModal.addEventListener('click', (e) => {
      if (e.target === this.elements.errorModal) {
        this.hideModal();
      }
    });

    // ESC key to close modal
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.elements.errorModal.style.display === 'block') {
        this.hideModal();
      }
    });
  }

  private async handleFormSubmit(data: PixFormData): Promise<void> {
    try {
      this.setLoading(true);
      this.currentFormData = data;

      // Create domain objects with validation
      const pixKey = PixKey.create(data.pixKey, data.keyType);
      const amount = Money.create(data.amount);
      
      const transactionParams: {
        pixKey: PixKey;
        receiverName: string;
        receiverCity: string;
        amount: Money;
        reference?: string;
        description?: string;
      } = {
        pixKey,
        receiverName: data.receiverName,
        receiverCity: data.receiverCity,
        amount
      };

      if (data.reference) {
        transactionParams.reference = data.reference;
      }

      if (data.description) {
        transactionParams.description = data.description;
      }

      const transaction = PixTransaction.create(transactionParams);

      // Generate QR Code
      const useCaseRequest: {
        transaction: PixTransaction;
        customization?: QRCustomizationOptions;
        isDynamic: boolean;
      } = {
        transaction,
        isDynamic: false
      };

      if (this.currentCustomization) {
        useCaseRequest.customization = this.currentCustomization;
      }

      const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);

      // Display result
      const displayData: {
        brCode: string;
        qrCodeDataUrl?: string;
        transaction: PixTransaction;
      } = {
        brCode: result.brCode,
        transaction: result.transaction
      };

      if (result.qrCodeDataUrl) {
        displayData.qrCodeDataUrl = result.qrCodeDataUrl;
      }

      this.qrDisplayComponent.displayQRResult(displayData);

      this.showToast('QR Code PIX gerado com sucesso!', 'success');

    } catch (error) {
      console.error('Error generating QR Code:', error);
      this.handleError(error);
    } finally {
      this.setLoading(false);
    }
  }

  private handleFormChange(data: Partial<PixFormData>): void {
    // Update current form data
    this.currentFormData = { ...this.currentFormData, ...data } as PixFormData;
    
    // If QR is already displayed and form is valid, regenerate
    if (this.qrDisplayComponent.getCurrentBRCode() && this.isFormComplete(data)) {
      this.regenerateQRIfNeeded();
    }
  }

  private handleKeyValidation(isValid: boolean, message: string): void {
    // Key validation feedback is handled by the form component
    // Could add additional logic here if needed
  }

  private handleCustomizationChange(options: QRCustomizationOptions): void {
    this.currentCustomization = options;
    this.regenerateQRIfNeeded();
  }

  private handleToggleCustomization(active: boolean): void {
    if (!active) {
      this.currentCustomization = null;
    }
    this.regenerateQRIfNeeded();
  }

  private handleImageUpload(file: File): void {
    // Image upload is handled within customization
    // Regeneration will be triggered by customization change
  }

  private handleMarginChange(margin: number): void {
    this.qrDisplayComponent.updateMarginStyles(margin);
    this.regenerateQRIfNeeded();
  }

  private async regenerateQRIfNeeded(): Promise<void> {
    if (!this.currentFormData || !this.isFormComplete(this.currentFormData)) {
      return;
    }

    try {
      // Create transaction from current form data
      const pixKey = PixKey.create(this.currentFormData.pixKey, this.currentFormData.keyType);
      const amount = Money.create(this.currentFormData.amount);
      
      const transactionParams: {
        pixKey: PixKey;
        receiverName: string;
        receiverCity: string;
        amount: Money;
        reference?: string;
        description?: string;
      } = {
        pixKey,
        receiverName: this.currentFormData.receiverName,
        receiverCity: this.currentFormData.receiverCity,
        amount
      };

      if (this.currentFormData.reference) {
        transactionParams.reference = this.currentFormData.reference;
      }

      if (this.currentFormData.description) {
        transactionParams.description = this.currentFormData.description;
      }

      const transaction = PixTransaction.create(transactionParams);

      // Regenerate QR Code
      const useCaseRequest: {
        transaction: PixTransaction;
        customization?: QRCustomizationOptions;
        isDynamic: boolean;
      } = {
        transaction,
        isDynamic: false
      };

      if (this.currentCustomization) {
        useCaseRequest.customization = this.currentCustomization;
      }

      const result = await this.generatePixQRCodeUseCase.execute(useCaseRequest);

      // Update display
      const displayData: {
        brCode: string;
        qrCodeDataUrl?: string;
        transaction: PixTransaction;
      } = {
        brCode: result.brCode,
        transaction: result.transaction
      };

      if (result.qrCodeDataUrl) {
        displayData.qrCodeDataUrl = result.qrCodeDataUrl;
      }

      this.qrDisplayComponent.displayQRResult(displayData);

    } catch (error) {
      console.error('Error regenerating QR Code:', error);
      // Don't show error toast for regeneration failures
    }
  }

  private async copyBRCode(brCode: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(brCode);
    } catch (error) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = brCode;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  }

  private async downloadQRCode(format: 'png' | 'svg'): Promise<void> {
    const brCode = this.qrDisplayComponent.getCurrentBRCode();
    if (!brCode) {
      throw new Error('No QR Code to download');
    }

    const filename = this.generateFilename(format);
    await this.qrDisplayComponent.downloadCurrentQR({
      filename,
      format,
      quality: 0.9
    });
  }

  private generateFilename(format: string): string {
    const isCustomized = this.qrCustomizationComponent.isCustomizationActive();
    const baseName = isCustomized ? 'qrcode-pix-personalizado' : 'qrcode-pix-padrao';
    return `${baseName}.${format}`;
  }

  private handleError(error: unknown): void {
    let message = 'Erro interno do servidor';

    if (error instanceof ValidationError) {
      message = error.message;
    } else if (error instanceof PixError) {
      message = error.message;
    } else if (error instanceof Error) {
      message = `Erro ao gerar QR Code: ${error.message}`;
    }

    this.showError(message);
  }

  private isFormComplete(data: Partial<PixFormData>): boolean {
    return !!(
      data.keyType &&
      data.pixKey?.trim() &&
      data.receiverName?.trim() &&
      data.receiverCity?.trim()
    );
  }

  private setLoading(loading: boolean): void {
    this.pixFormComponent.setLoading(loading);
    this.qrDisplayComponent.setLoading(loading);
  }

  private showError(message: string): void {
    this.elements.errorMessage.textContent = message;
    this.elements.errorModal.style.display = 'block';
  }

  private hideModal(): void {
    this.elements.errorModal.style.display = 'none';
  }

  private showToast(message: string, type: 'success' | 'error' | 'warning'): void {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);

    // Hide toast
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => {
        if (toast.parentNode) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  // Public API for external control
  public applyPreset(presetName: string): void {
    this.qrCustomizationComponent.applyPreset(presetName);
  }

  public reset(): void {
    this.pixFormComponent.reset();
    this.qrCustomizationComponent.reset();
    this.qrDisplayComponent.hideResult();
    this.currentFormData = null;
    this.currentCustomization = null;
  }

  public getCurrentFormData(): PixFormData | null {
    return this.currentFormData;
  }

  public getCurrentCustomization(): QRCustomizationOptions | null {
    return this.currentCustomization;
  }
}

// Global function for preset application (maintains compatibility with original code)
declare global {
  interface Window {
    pixGenerator: AppController;
    applyPreset: (presetName: string) => void;
  }
}

export function initializeApp(elements: AppControllerElements): AppController {
  const app = new AppController(elements);
  
  // Set global references for compatibility
  window.pixGenerator = app;
  window.applyPreset = (presetName: string) => app.applyPreset(presetName);
  
  return app;
}