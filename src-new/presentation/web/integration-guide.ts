/**
 * Integration Guide for Legacy HTML
 * Provides easy migration path from the old app.js to the new architecture
 */

import { App<PERSON>ontroller, AppControllerElements } from './app-controller';

/**
 * Automatically initializes the new architecture with existing HTML elements
 * This provides a drop-in replacement for the old PixQRGeneratorIntegrated class
 */
export function initializeFromHTML(): AppController {
  // Get all required elements from the existing HTML
  const elements: AppControllerElements = {
    // Form elements
    form: document.getElementById('pixForm') as HTMLFormElement,
    keyTypeSelect: document.getElementById('keyType') as HTMLSelectElement,
    pixKeyInput: document.getElementById('pixKey') as HTMLInputElement,
    receiverNameInput: document.getElementById('receiverName') as HTMLInputElement,
    receiverCityInput: document.getElementById('receiverCity') as HTMLInputElement,
    amountInput: document.getElementById('amount') as HTMLInputElement,
    referenceInput: document.getElementById('reference') as HTMLInputElement,
    descriptionInput: document.getElementById('description') as HTMLTextAreaElement,
    generateBtn: document.getElementById('generateBtn') as HTMLButtonElement,
    keyValidation: document.getElementById('keyValidation') as HTMLElement,

    // Customization elements
    customizationToggle: document.getElementById('customizationToggle') as HTMLButtonElement,
    customizationPanel: document.getElementById('customizationPanel') as HTMLElement,
    dotsType: document.getElementById('dots-type') as HTMLSelectElement,
    dotsColor: document.getElementById('dots-color') as HTMLInputElement,
    cornerSquareType: document.getElementById('corner-square-type') as HTMLSelectElement,
    cornerSquareColor: document.getElementById('corner-square-color') as HTMLInputElement,
    cornerDotType: document.getElementById('corner-dot-type') as HTMLSelectElement,
    cornerDotColor: document.getElementById('corner-dot-color') as HTMLInputElement,
    backgroundColor: document.getElementById('background-color') as HTMLInputElement,
    qrSize: document.getElementById('qr-size') as HTMLSelectElement,
    imageSize: document.getElementById('image-size') as HTMLInputElement,
    imageSizeValue: document.getElementById('image-size-value') as HTMLElement,
    imageMargin: document.getElementById('image-margin') as HTMLInputElement,
    hideBackgroundDots: document.getElementById('hide-background-dots') as HTMLInputElement,
    centerImage: document.getElementById('center-image') as HTMLInputElement,

    // Display elements
    qrPlaceholder: document.getElementById('qrPlaceholder') as HTMLElement,
    qrResult: document.getElementById('qrResult') as HTMLElement,
    qrPreview: document.getElementById('qr-preview') as HTMLElement,
    brCodeText: document.getElementById('brCodeText') as HTMLElement,
    pixDetails: document.getElementById('pixDetails') as HTMLElement,
    downloadBtn: document.getElementById('downloadBtn') as HTMLButtonElement,
    downloadSvgBtn: document.getElementById('downloadSvgBtn') as HTMLButtonElement,
    copyBtn: document.getElementById('copyBtn') as HTMLButtonElement,
    loadingSpinner: document.getElementById('loadingSpinner') as HTMLElement,

    // Modal elements
    errorModal: document.getElementById('errorModal') as HTMLElement,
    errorMessage: document.getElementById('errorMessage') as HTMLElement,
    closeModal: document.getElementById('closeModal') as HTMLButtonElement
  };

  // Validate all required elements exist
  validateElements(elements);

  // Initialize and return the app controller
  return new AppController(elements);
}

/**
 * Validates that all required HTML elements exist
 */
function validateElements(elements: AppControllerElements): void {
  const missingElements: string[] = [];

  Object.entries(elements).forEach(([key, element]) => {
    if (!element) {
      missingElements.push(key);
    }
  });

  if (missingElements.length > 0) {
    throw new Error(
      `Missing required HTML elements: ${missingElements.join(', ')}. ` +
      'Make sure your HTML contains all required elements with correct IDs.'
    );
  }
}

/**
 * Legacy compatibility function
 * Maintains the same global interface as the original app.js
 */
export function initializeLegacyCompatibility(): void {
  document.addEventListener('DOMContentLoaded', () => {
    try {
      const appController = initializeFromHTML();
      
      // Maintain backward compatibility
      (window as any).pixGenerator = appController;
      (window as any).applyPreset = (presetName: string) => {
        appController.applyPreset(presetName);
      };

      console.log('✅ PIX QR Generator initialized successfully with Clean Architecture');
    } catch (error) {
      console.error('❌ Failed to initialize PIX QR Generator:', error);
      
      // Fallback error message for users
      const errorDiv = document.createElement('div');
      errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
        max-width: 300px;
      `;
      errorDiv.innerHTML = `
        <strong>Erro de Inicialização</strong><br>
        Verifique se todos os elementos HTML necessários estão presentes.
      `;
      document.body.appendChild(errorDiv);
      
      setTimeout(() => {
        document.body.removeChild(errorDiv);
      }, 5000);
    }
  });
}

/**
 * Development helper to check element availability
 */
export function checkHTMLCompatibility(): { 
  compatible: boolean; 
  missing: string[]; 
  suggestions: string[] 
} {
  const requiredIds = [
    'pixForm', 'keyType', 'pixKey', 'receiverName', 'receiverCity',
    'amount', 'reference', 'description', 'generateBtn', 'keyValidation',
    'customizationToggle', 'customizationPanel', 'dots-type', 'dots-color',
    'corner-square-type', 'corner-square-color', 'corner-dot-type',
    'corner-dot-color', 'background-color', 'qr-size', 'image-size',
    'image-size-value', 'image-margin', 'hide-background-dots',
    'center-image', 'qrPlaceholder', 'qrResult', 'qr-preview',
    'brCodeText', 'pixDetails', 'downloadBtn', 'downloadSvgBtn',
    'copyBtn', 'loadingSpinner', 'errorModal', 'errorMessage', 'closeModal'
  ];

  const missing = requiredIds.filter(id => !document.getElementById(id));
  const suggestions = missing.map(id => 
    `Add: <element id="${id}">...</element>`
  );

  return {
    compatible: missing.length === 0,
    missing,
    suggestions
  };
}

// Auto-initialize when this module is imported (for easy migration)
if (typeof window !== 'undefined') {
  initializeLegacyCompatibility();
}