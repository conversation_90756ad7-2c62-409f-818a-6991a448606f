<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>QR Code PIX - Clean Architecture Demo</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 5px;
      }

      .badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-top: 10px;
      }

      .main-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        padding: 40px;
      }

      .form-section h2 {
        color: #2d3748;
        font-size: 1.5rem;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #4a5568;
      }

      .form-group select,
      .form-group input,
      .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
      }

      .form-group select:focus,
      .form-group input:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .validation-message {
        margin-top: 5px;
        font-size: 14px;
        padding: 8px;
        border-radius: 4px;
      }

      .validation-message.success {
        background: #f0fff4;
        color: #38a169;
        border: 1px solid #9ae6b4;
      }

      .validation-message.error {
        background: #fed7d7;
        color: #c53030;
        border: 1px solid #feb2b2;
      }

      .char-counter {
        font-size: 12px;
        color: #718096;
        text-align: right;
        margin-top: 5px;
      }

      .generate-btn {
        width: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 16px 24px;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease;
      }

      .generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .generate-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .generate-btn.loading {
        position: relative;
        color: transparent;
      }

      .generate-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }

      .result-section {
        text-align: center;
      }

      .result-section h2 {
        color: #2d3748;
        font-size: 1.5rem;
        margin-bottom: 20px;
      }

      .qr-container {
        background: #f7fafc;
        border: 2px dashed #e2e8f0;
        border-radius: 12px;
        padding: 40px 20px;
        margin-bottom: 20px;
        min-height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      .qr-placeholder {
        color: #a0aec0;
        font-size: 16px;
      }

      .qr-code {
        max-width: 100%;
        border-radius: 8px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
      }

      .br-code {
        background: #1a202c;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        word-break: break-all;
        line-height: 1.4;
        margin-bottom: 20px;
      }

      .actions {
        display: flex;
        gap: 10px;
        justify-content: center;
      }

      .action-btn {
        background: white;
        color: #667eea;
        border: 2px solid #667eea;
        padding: 10px 20px;
        border-radius: 6px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .action-btn:hover {
        background: #667eea;
        color: white;
      }

      .customization-section {
        grid-column: 1 / -1;
        margin-top: 20px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 12px;
      }

      .customization-toggle {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .customization-toggle:hover {
        border-color: #667eea;
      }

      .toggle-switch {
        width: 50px;
        height: 26px;
        background: #cbd5e0;
        border-radius: 13px;
        position: relative;
        transition: background 0.3s ease;
      }

      .toggle-switch.active {
        background: #667eea;
      }

      .toggle-slider {
        width: 22px;
        height: 22px;
        background: white;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .toggle-switch.active .toggle-slider {
        transform: translateX(24px);
      }

      .toggle-label {
        font-weight: 600;
        color: #2d3748;
        font-size: 16px;
      }

      .toggle-description {
        color: #718096;
        font-size: 14px;
        margin-top: 2px;
      }

      .customization-panel {
        display: none;
        animation: slideDown 0.3s ease;
      }

      .customization-panel.active {
        display: block;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .section-title {
        font-weight: 600;
        color: #2d3748;
        font-size: 16px;
        margin: 25px 0 15px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;
      }

      .section-title:first-child {
        margin-top: 0;
      }

      .customization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
      }

      .preset-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-bottom: 25px;
      }

      .preset-btn {
        background: #edf2f7;
        border: 1px solid #e2e8f0;
        padding: 10px 16px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .preset-btn:hover,
      .preset-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
      }

      .image-upload-label {
        display: inline-block;
        background: #edf2f7;
        border: 2px dashed #cbd5e0;
        padding: 20px;
        border-radius: 8px;
        cursor: pointer;
        text-align: center;
        transition: all 0.3s ease;
        width: 100%;
        box-sizing: border-box;
      }

      .image-upload-label:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }

      .range-value {
        text-align: center;
        font-weight: 600;
        color: #667eea;
        margin-top: 5px;
        font-size: 14px;
      }

      input[type='range'] {
        width: 100%;
        height: 6px;
        border-radius: 3px;
        background: #e2e8f0;
        outline: none;
        -webkit-appearance: none;
      }

      input[type='range']::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      input[type='range']::-moz-range-thumb {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      input[type='color'] {
        width: 50px;
        height: 40px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      input[type='checkbox'] {
        margin-right: 8px;
        transform: scale(1.2);
      }

      .performance-info {
        background: #f0fff4;
        border: 1px solid #9ae6b4;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 14px;
      }

      .architecture-info {
        background: #ebf8ff;
        border: 1px solid #90cdf4;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .main-content {
          grid-template-columns: 1fr;
          gap: 20px;
          padding: 20px;
        }

        .header h1 {
          font-size: 1.8rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎨 QR Code PIX Generator</h1>
        <p>Refatorado com Clean Architecture</p>
        <p>TypeScript • Domain-Driven Design • Performance Otimizada</p>
        <span class="badge">✨ Versão 2.0 - Clean Architecture</span>
      </div>

      <div class="main-content">
        <div class="form-section">
          <h2>📝 Dados PIX</h2>

          <form id="pixForm">
            <div class="form-group">
              <label for="keyType">Tipo de Chave PIX:</label>
              <select id="keyType">
                <option value="cpf">CPF</option>
                <option value="phone">Telefone</option>
                <option value="email">Email</option>
                <option value="random">Chave Aleatória</option>
              </select>
            </div>

            <div class="form-group">
              <label for="pixKey">Chave PIX:</label>
              <input id="pixKey" type="text" placeholder="000.000.000-00" maxlength="14" />
              <div id="keyValidation" class="validation-message"></div>
            </div>

            <div class="form-group">
              <label for="receiverName">Nome do Recebedor:</label>
              <input
                id="receiverName"
                type="text"
                placeholder="João Silva"
                maxlength="25"
                required
              />
              <div class="char-counter">0/25 caracteres</div>
            </div>

            <div class="form-group">
              <label for="receiverCity">Cidade:</label>
              <input
                id="receiverCity"
                type="text"
                placeholder="São Paulo"
                maxlength="15"
                required
              />
              <div class="char-counter">0/15 caracteres</div>
            </div>

            <div class="form-group">
              <label for="amount">Valor (opcional):</label>
              <input id="amount" type="text" placeholder="R$ 0,00" />
            </div>

            <div class="form-group">
              <label for="reference">Referência:</label>
              <input id="reference" type="text" placeholder="REF001" />
            </div>

            <div class="form-group">
              <label for="description">Descrição:</label>
              <textarea id="description" placeholder="Descrição do pagamento" rows="3"></textarea>
            </div>

            <button id="generateBtn" type="submit" class="generate-btn">
              🚀 Gerar QR Code PIX
            </button>
          </form>
        </div>

        <div class="result-section">
          <h2>📱 Resultado</h2>

          <div class="qr-container" id="qrContainer">
            <div class="qr-placeholder">
              <p>🎯 QR Code aparecerá aqui</p>
              <p style="font-size: 14px; margin-top: 10px">
                Preencha os dados e clique em "Gerar QR Code"
              </p>
            </div>
          </div>

          <div id="brCodeContainer" style="display: none">
            <div class="br-code" id="brCode"></div>
          </div>

          <div class="actions" id="actions" style="display: none">
            <button class="action-btn" onclick="downloadQRCode()">⬇️ Download PNG</button>
            <button class="action-btn" onclick="copyBRCode()">📋 Copiar BR Code</button>
            <button class="action-btn" onclick="resetForm()">🔄 Novo QR</button>
          </div>
        </div>

        <div class="customization-section">
          <h2>🎨 Personalização</h2>

          <!-- Toggle para personalização -->
          <div class="customization-toggle">
            <div class="toggle-switch" id="customizationToggle">
              <div class="toggle-slider"></div>
            </div>
            <div>
              <div class="toggle-label">Personalizar QR Code</div>
              <div class="toggle-description">Ative para acessar opções avançadas de estilo</div>
            </div>
          </div>

          <!-- Painel de personalização -->
          <div class="customization-panel" id="customizationPanel">
            <div class="preset-buttons">
              <button class="preset-btn active" data-preset="modern">🌟 Modern</button>
              <button class="preset-btn" data-preset="classic">📋 Clássico</button>
              <button class="preset-btn" data-preset="elegant">✨ Elegante</button>
              <button class="preset-btn" data-preset="vibrant">🌈 Vibrante</button>
              <button class="preset-btn" data-preset="circular">⭕ Circular</button>
            </div>

            <div class="section-title">🎨 Estilo dos Pontos</div>
            <div class="customization-grid">
              <div class="form-group">
                <label for="dotsType">Tipo dos Pontos:</label>
                <select id="dotsType">
                  <option value="square">Quadrado</option>
                  <option value="rounded" selected>Arredondado</option>
                  <option value="dots">Círculo</option>
                  <option value="classy">Clássico</option>
                  <option value="classy-rounded">Clássico Arredondado</option>
                  <option value="extra-rounded">Extra Arredondado</option>
                </select>
              </div>

              <div class="form-group">
                <label for="dotsColor">Cor dos Pontos:</label>
                <input id="dotsColor" type="color" value="#000000" />
              </div>
            </div>

            <div class="section-title">📐 Position Patterns (Cantos)</div>
            <div class="customization-grid">
              <div class="form-group">
                <label for="cornerSquareType">Tipo dos Quadrados:</label>
                <select id="cornerSquareType">
                  <option value="">Padrão</option>
                  <option value="square">Quadrado</option>
                  <option value="extra-rounded">Extra Arredondado</option>
                  <option value="dot">Ponto</option>
                  <option value="classy">Clássico</option>
                  <option value="classy-rounded">Clássico Arredondado</option>
                </select>
              </div>
              <div class="form-group">
                <label for="cornerSquareColor">Cor dos Quadrados:</label>
                <input id="cornerSquareColor" type="color" value="#000000" />
              </div>
            </div>

            <div class="customization-grid">
              <div class="form-group">
                <label for="cornerDotType">Tipo dos Pontos Centrais:</label>
                <select id="cornerDotType">
                  <option value="">Padrão</option>
                  <option value="square">Quadrado</option>
                  <option value="dot">Ponto</option>
                  <option value="classy">Clássico</option>
                  <option value="classy-rounded">Clássico Arredondado</option>
                  <option value="extra-rounded">Extra Arredondado</option>
                </select>
              </div>
              <div class="form-group">
                <label for="cornerDotColor">Cor dos Pontos Centrais:</label>
                <input id="cornerDotColor" type="color" value="#000000" />
              </div>
            </div>

            <div class="section-title">🖼️ Fundo</div>
            <div class="form-group">
              <label for="backgroundColor">Cor de Fundo:</label>
              <input id="backgroundColor" type="color" value="#ffffff" />
            </div>

            <div class="section-title">🏷️ Imagem Central</div>
            <div class="form-group">
              <label class="image-upload-label" for="centerImage">
                Escolher imagem<br /><small>PNG, JPG até 5MB</small>
              </label>
              <input id="centerImage" type="file" accept="image/*" style="display: none" />
            </div>

            <div class="customization-grid">
              <div class="form-group">
                <label for="imageSize">Tamanho da Imagem:</label>
                <input id="imageSize" type="range" min="0.1" max="0.8" step="0.1" value="0.4" />
                <div class="range-value" id="imageSizeValue">40%</div>
              </div>
              <div class="form-group">
                <label for="imageMargin">Margem da Imagem:</label>
                <input id="imageMargin" type="number" min="0" max="50" value="0" />
              </div>
            </div>

            <div class="form-group">
              <label>
                <input id="hideBackgroundDots" type="checkbox" checked />
                Ocultar pontos sob a imagem
              </label>
            </div>

            <div class="section-title">📏 Dimensões</div>
            <div class="form-group">
              <label for="qrSize">Tamanho (px):</label>
              <select id="qrSize">
                <option value="100">100 × 100 px</option>
                <option value="200">200 × 200 px</option>
                <option value="300" selected>300 × 300 px</option>
                <option value="400">400 × 400 px</option>
                <option value="500">500 × 500 px</option>
                <option value="600">600 × 600 px</option>
                <option value="700">700 × 700 px</option>
                <option value="800">800 × 800 px</option>
                <option value="900">900 × 900 px</option>
                <option value="1000">1000 × 1000 px</option>
                <option value="1100">1100 × 1100 px</option>
                <option value="1200">1200 × 1200 px</option>
                <option value="1300">1300 × 1300 px</option>
                <option value="1400">1400 × 1400 px</option>
                <option value="1500">1500 × 1500 px</option>
                <option value="1600">1600 × 1600 px</option>
                <option value="1700">1700 × 1700 px</option>
                <option value="1800">1800 × 1800 px</option>
                <option value="1900">1900 × 1900 px</option>
                <option value="2000">2000 × 2000 px</option>
              </select>
            </div>
            <div class="form-group">
              <small style="color: #6c757d; font-style: italic; display: block; margin-top: 5px">
                ℹ️ As dimensões são sempre quadradas e variam de 100px a 2000px. Valores aplicados
                no download; pré-visualização limitada a 400x400px.
              </small>
            </div>

            <!-- Campos ocultos para compatibilidade -->
            <input type="hidden" id="qrWidth" value="300" />
            <input type="hidden" id="qrHeight" value="300" />
          </div>
        </div>
      </div>

      <div class="architecture-info">
        <strong>🏛️ Clean Architecture:</strong> Este demo utiliza a nova arquitetura refatorada com
        separação em camadas: Domain (PixKey, Money, PixTransaction) → Application
        (GeneratePixQRCodeUseCase) → Infrastructure (QRGenerators) → Presentation (Components).
      </div>

      <div class="performance-info" id="performanceInfo" style="display: none">
        <strong>⚡ Performance:</strong> <span id="performanceDetails"></span>
      </div>
    </div>

    <!-- QR Code Styling Library -->
    <script src="qr-code-styling.js"></script>

    <script type="module">
      // Este script simula a integração com o projeto refatorado
      // Em produção, seria importado dos arquivos TypeScript compilados

      class PIXFormManager {
        constructor() {
          this.form = document.getElementById('pixForm');
          this.elements = this.getFormElements();
          this.currentPreset = 'modern';
          this.customizationActive = false;
          this.currentImageFile = null;
          this.setupEventListeners();
          this.setupMasks();
          this.setupCustomization();
          this.applyPreset('modern');
        }

        getFormElements() {
          return {
            form: document.getElementById('pixForm'),
            keyTypeSelect: document.getElementById('keyType'),
            pixKeyInput: document.getElementById('pixKey'),
            receiverNameInput: document.getElementById('receiverName'),
            receiverCityInput: document.getElementById('receiverCity'),
            amountInput: document.getElementById('amount'),
            referenceInput: document.getElementById('reference'),
            descriptionInput: document.getElementById('description'),
            generateBtn: document.getElementById('generateBtn'),
            keyValidation: document.getElementById('keyValidation'),
            // Customization elements
            customizationToggle: document.getElementById('customizationToggle'),
            customizationPanel: document.getElementById('customizationPanel'),
            dotsType: document.getElementById('dotsType'),
            dotsColor: document.getElementById('dotsColor'),
            cornerSquareType: document.getElementById('cornerSquareType'),
            cornerSquareColor: document.getElementById('cornerSquareColor'),
            cornerDotType: document.getElementById('cornerDotType'),
            cornerDotColor: document.getElementById('cornerDotColor'),
            backgroundColor: document.getElementById('backgroundColor'),
            qrSize: document.getElementById('qrSize'),
            imageSize: document.getElementById('imageSize'),
            imageSizeValue: document.getElementById('imageSizeValue'),
            imageMargin: document.getElementById('imageMargin'),
            hideBackgroundDots: document.getElementById('hideBackgroundDots'),
            centerImage: document.getElementById('centerImage'),
          };
        }

        setupEventListeners() {
          // Form submission
          this.form.addEventListener('submit', e => {
            e.preventDefault();
            this.handleFormSubmit();
          });

          // PIX key type change
          this.elements.keyTypeSelect.addEventListener('change', e => {
            this.updatePixKeyInput(e.target.value);
          });

          // PIX key validation
          this.elements.pixKeyInput.addEventListener('input', e => {
            this.validatePixKey(e.target.value);
          });

          // Character counters
          this.elements.receiverNameInput.addEventListener('input', e => {
            this.updateCharCounter(e.target, 25);
          });

          this.elements.receiverCityInput.addEventListener('input', e => {
            this.updateCharCounter(e.target, 15);
          });

          // Preset buttons
          document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', e => {
              this.applyPreset(e.target.dataset.preset);
            });
          });
        }

        setupCustomization() {
          // Toggle switch
          this.elements.customizationToggle.addEventListener('click', () => {
            this.toggleCustomization();
          });

          // Customization controls
          const customizationInputs = [
            'dotsType',
            'dotsColor',
            'cornerSquareType',
            'cornerSquareColor',
            'cornerDotType',
            'cornerDotColor',
            'backgroundColor',
            'qrSize',
            'imageSize',
            'imageMargin',
            'hideBackgroundDots',
          ];

          customizationInputs.forEach(id => {
            const element = this.elements[id];
            if (element) {
              if (element.type === 'range') {
                element.addEventListener('input', () => this.updateRangeValue(id));
              }

              if (id === 'qrSize') {
                element.addEventListener('change', e => this.handleSizeChange(e));
              }

              element.addEventListener('change', () => this.handleCustomizationChange());
              element.addEventListener('input', () => this.handleCustomizationChange());
            }
          });

          // Image upload
          this.elements.centerImage.addEventListener('change', e => {
            this.handleImageUpload(e);
          });

          // Initialize range values
          this.updateRangeValue('imageSize');
        }

        toggleCustomization() {
          this.customizationActive = !this.customizationActive;
          this.elements.customizationToggle.classList.toggle('active', this.customizationActive);
          this.elements.customizationPanel.classList.toggle('active', this.customizationActive);
        }

        handleCustomizationChange() {
          // Regenerate QR if already displayed
          if (document.getElementById('qrContainer').querySelector('canvas, svg')) {
            this.regenerateQRCode();
          }
        }

        handleSizeChange(event) {
          const size = parseInt(event.target.value);

          // Update hidden width/height fields for compatibility
          const widthField = document.getElementById('qrWidth');
          const heightField = document.getElementById('qrHeight');

          if (widthField) widthField.value = size;
          if (heightField) heightField.value = size;

          this.handleCustomizationChange();
        }

        handleImageUpload(event) {
          const file = event.target.files?.[0];

          if (file) {
            this.currentImageFile = file;

            // Update label text
            const label = document.querySelector('.image-upload-label');
            if (label) {
              label.innerHTML = `✅ ${file.name}<br><small>Clique para alterar</small>`;
            }

            this.handleCustomizationChange();
          }
        }

        updateRangeValue(inputId) {
          const input = this.elements[inputId];
          if (!input) return;

          let valueElement = null;
          let displayValue = '';

          switch (inputId) {
            case 'imageSize':
              valueElement = this.elements.imageSizeValue;
              displayValue = Math.round(parseFloat(input.value) * 100) + '%';
              break;
          }

          if (valueElement) {
            valueElement.textContent = displayValue;
          }
        }

        setupMasks() {
          // CPF mask
          this.elements.pixKeyInput.addEventListener('input', e => {
            if (this.elements.keyTypeSelect.value === 'cpf') {
              e.target.value = this.applyCPFMask(e.target.value);
            } else if (this.elements.keyTypeSelect.value === 'phone') {
              e.target.value = this.applyPhoneMask(e.target.value);
            }
          });

          // Currency mask for amount
          this.elements.amountInput.addEventListener('input', e => {
            e.target.value = this.applyCurrencyMask(e.target.value);
          });
        }

        updatePixKeyInput(keyType) {
          const input = this.elements.pixKeyInput;
          input.value = '';
          this.elements.keyValidation.textContent = '';
          this.elements.keyValidation.className = 'validation-message';

          switch (keyType) {
            case 'cpf':
              input.placeholder = '000.000.000-00';
              input.type = 'text';
              input.maxLength = 14;
              break;
            case 'phone':
              input.placeholder = '(11) 99999-9999';
              input.type = 'tel';
              input.maxLength = 15;
              break;
            case 'email':
              input.placeholder = '<EMAIL>';
              input.type = 'email';
              input.maxLength = 50;
              break;
            case 'random':
              input.placeholder = 'chave-aleatoria-uuid';
              input.type = 'text';
              input.maxLength = 50;
              break;
          }
        }

        validatePixKey(value) {
          const keyType = this.elements.keyTypeSelect.value;
          const validation = this.elements.keyValidation;

          if (!value) {
            validation.textContent = '';
            validation.className = 'validation-message';
            return;
          }

          let isValid = false;
          let message = '';

          switch (keyType) {
            case 'cpf':
              isValid = this.validateCPF(value);
              message = isValid ? 'CPF válido' : 'CPF inválido';
              break;
            case 'email':
              isValid = this.validateEmail(value);
              message = isValid ? 'Email válido' : 'Email inválido';
              break;
            case 'phone':
              isValid = this.validatePhone(value);
              message = isValid ? 'Telefone válido' : 'Telefone inválido';
              break;
            case 'random':
              isValid = value.length >= 10;
              message = isValid ? 'Chave válida' : 'Mínimo 10 caracteres';
              break;
          }

          validation.textContent = message;
          validation.className = `validation-message ${isValid ? 'success' : 'error'}`;
        }

        updateCharCounter(input, maxLength) {
          const counter = input.parentElement.querySelector('.char-counter');
          if (counter) {
            counter.textContent = `${input.value.length}/${maxLength} caracteres`;
          }
        }

        async handleFormSubmit() {
          const startTime = performance.now();

          // Set loading state
          this.elements.generateBtn.disabled = true;
          this.elements.generateBtn.classList.add('loading');

          try {
            // Simulate domain validation and BR-Code generation
            const formData = this.getFormData();

            if (!this.validateFormData(formData)) {
              return;
            }

            // Simulate BR-Code generation (Domain Service)
            await this.delay(500); // Simulate processing time
            const brCode = this.generateBRCode(formData);

            // Simulate QR Code generation (Infrastructure)
            await this.delay(800);
            const qrCodeDataUrl = await this.generateQRCode(brCode);

            // Display results
            this.displayResults(brCode, qrCodeDataUrl);

            // Show performance info
            const endTime = performance.now();
            const duration = (endTime - startTime).toFixed(2);
            this.showPerformanceInfo(duration);
          } catch (error) {
            console.error('Error generating QR code:', error);
            alert('Erro ao gerar QR Code. Tente novamente.');
          } finally {
            // Reset loading state
            this.elements.generateBtn.disabled = false;
            this.elements.generateBtn.classList.remove('loading');
          }
        }

        getFormData() {
          // Clean PIX key based on type (remove formatting)
          let pixKey = this.elements.pixKeyInput.value.trim();
          const keyType = this.elements.keyTypeSelect.value;

          if (keyType === 'cpf') {
            pixKey = pixKey.replace(/\D/g, ''); // Remove all non-digits
          } else if (keyType === 'phone') {
            pixKey = pixKey.replace(/\D/g, ''); // Remove all non-digits
            if (!pixKey.startsWith('55')) {
              pixKey = '55' + pixKey;
            }
            if (!pixKey.startsWith('+')) {
              pixKey = '+' + pixKey;
            }
          }

          return {
            keyType: keyType,
            pixKey: pixKey,
            receiverName: this.elements.receiverNameInput.value,
            receiverCity: this.elements.receiverCityInput.value,
            amount: this.parseAmount(this.elements.amountInput.value),
            reference: this.elements.referenceInput.value,
            description: this.elements.descriptionInput.value,
          };
        }

        validateFormData(data) {
          if (!data.pixKey || !data.receiverName || !data.receiverCity) {
            alert('Por favor, preencha os campos obrigatórios.');
            return false;
          }
          return true;
        }

        generateBRCode(data) {
          // Simulate BRCodeGenerator domain service
          const fields = [
            '000201', // Payload Format Indicator
            '010211', // Point of Initiation Method
            this.createAccountInformation(data),
            '********', // Merchant Category Code
            '5303986', // Currency Code (BRL)
            data.amount > 0 ? this.formatBRCodeField('54', data.amount.toFixed(2)) : '',
            '5802BR', // Country Code
            this.formatBRCodeField('59', this.formatText(data.receiverName)),
            this.formatBRCodeField('60', this.formatText(data.receiverCity)),
            this.createAdditionalDataField(data),
            '6304', // CRC placeholder
          ]
            .filter(Boolean)
            .join('');

          // Calculate CRC16 (simplified)
          const crc = this.calculateCRC16(fields);
          return fields + crc;
        }

        formatBRCodeField(tag, value) {
          const length = String(value.length).padStart(2, '0');
          return `${tag}${length}${value}`;
        }

        createAccountInformation(data) {
          const basePix = this.formatBRCodeField('00', 'br.gov.bcb.pix');
          let infoString = this.formatBRCodeField('01', data.pixKey);

          if (data.description) {
            infoString += this.formatBRCodeField('02', this.formatText(data.description));
          }

          return this.formatBRCodeField('26', basePix + infoString);
        }

        createAdditionalDataField(data) {
          const txid = data.reference ? this.formatText(data.reference) : '***';
          return this.formatBRCodeField('62', this.formatBRCodeField('05', txid));
        }

        formatText(text) {
          return text
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
            .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep only allowed characters
            .trim();
        }

        calculateCRC16(data) {
          // Simplified CRC16 calculation for demo
          let crc = 0xffff;
          for (let i = 0; i < data.length; i++) {
            crc ^= data.charCodeAt(i) << 8;
            for (let j = 0; j < 8; j++) {
              if (crc & 0x8000) {
                crc = (crc << 1) ^ 0x1021;
              } else {
                crc <<= 1;
              }
            }
          }
          return (crc & 0xffff).toString(16).toUpperCase().padStart(4, '0');
        }

        async generateQRCode(brCode) {
          // Wait for library to load if needed
          await this.waitForQRCodeStyling();

          try {
            if (typeof window.QRCodeStyling === 'undefined') {
              throw new Error('QRCodeStyling library not loaded');
            }

            const options = this.customizationActive
              ? await this.buildCustomizationOptionsAsync(brCode)
              : this.buildStandardOptions(brCode);

            return new Promise((resolve, reject) => {
              const qrCode = new window.QRCodeStyling(options);

              // Create temporary container
              const container = document.createElement('div');
              container.style.position = 'absolute';
              container.style.left = '-9999px';
              document.body.appendChild(container);

              qrCode.append(container);

              setTimeout(() => {
                const canvas = container.querySelector('canvas');
                if (canvas) {
                  const dataUrl = canvas.toDataURL();
                  document.body.removeChild(container);
                  resolve(dataUrl);
                } else {
                  document.body.removeChild(container);
                  reject(new Error('Failed to generate canvas'));
                }
              }, 200); // Increased timeout for image processing
            });
          } catch (error) {
            throw error;
          }
        }

        buildCustomizationOptions(brCode) {
          const size = parseInt(this.elements.qrSize.value) || 300;
          const maxDisplaySize = 400;
          const displaySize = Math.min(size, maxDisplaySize);

          const options = {
            width: displaySize,
            height: displaySize,
            type: 'canvas',
            data: brCode,
            margin: 10,
            qrOptions: {
              typeNumber: 0,
              mode: undefined,
              errorCorrectionLevel: 'M',
            },
            dotsOptions: {
              color: this.elements.dotsColor.value,
              type: this.elements.dotsType.value,
              roundSize: true,
            },
            backgroundOptions: {
              color: this.elements.backgroundColor.value,
              round: 0,
            },
            cornersSquareOptions: {
              color: this.elements.cornerSquareColor.value,
              type: this.elements.cornerSquareType.value || undefined,
            },
            cornersDotOptions: {
              color: this.elements.cornerDotColor.value,
              type: this.elements.cornerDotType.value || undefined,
            },
          };

          return options;
        }

        async buildCustomizationOptionsAsync(brCode) {
          const options = this.buildCustomizationOptions(brCode);

          // Add image options if image is uploaded
          if (this.currentImageFile) {
            const imageDataUrl = await this.fileToDataUrl(this.currentImageFile);
            options.image = imageDataUrl;
            options.imageOptions = {
              crossOrigin: 'anonymous',
              margin: parseInt(this.elements.imageMargin.value) || 0,
              imageSize: parseFloat(this.elements.imageSize.value) || 0.4,
              hideBackgroundDots: this.elements.hideBackgroundDots.checked,
            };
          }

          return options;
        }

        fileToDataUrl(file) {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(new Error('Failed to read file'));
            reader.readAsDataURL(file);
          });
        }

        buildStandardOptions(brCode) {
          return {
            width: 300,
            height: 300,
            type: 'canvas',
            data: brCode,
            margin: 10,
            qrOptions: {
              typeNumber: 0,
              mode: undefined,
              errorCorrectionLevel: 'M',
            },
            dotsOptions: {
              color: '#000000',
              type: 'square',
              roundSize: true,
            },
            backgroundOptions: {
              color: '#ffffff',
              round: 0,
            },
            cornersSquareOptions: {
              color: '#000000',
            },
            cornersDotOptions: {
              color: '#000000',
            },
          };
        }

        regenerateQRCode() {
          const brCodeElement = document.getElementById('brCode');
          if (brCodeElement && brCodeElement.textContent) {
            this.generateQRCode(brCodeElement.textContent)
              .then(dataUrl => {
                this.displayQRCode(dataUrl);
              })
              .catch(error => {
                console.error('Error regenerating QR code:', error);
              });
          }
        }

        displayQRCode(dataUrl) {
          const qrContainer = document.getElementById('qrContainer');
          qrContainer.innerHTML = `<img src="${dataUrl}" alt="QR Code PIX" style="max-width: 100%; border-radius: 8px;">`;
        }

        async waitForQRCodeStyling() {
          // Wait for QRCodeStyling library to be available
          let attempts = 0;
          const maxAttempts = 50; // 5 seconds max

          while (typeof window.QRCodeStyling === 'undefined' && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
          }

          if (typeof window.QRCodeStyling === 'undefined') {
            throw new Error('QRCodeStyling library failed to load');
          }
        }

        displayResults(brCode, qrCodeUrl) {
          // Display QR Code
          const qrContainer = document.getElementById('qrContainer');
          qrContainer.innerHTML = `<img src="${qrCodeUrl}" alt="QR Code PIX" class="qr-code" crossorigin="anonymous">`;

          // Display BR Code
          const brCodeContainer = document.getElementById('brCodeContainer');
          const brCodeElement = document.getElementById('brCode');
          brCodeElement.textContent = brCode;
          brCodeContainer.style.display = 'block';

          // Show actions
          document.getElementById('actions').style.display = 'flex';
        }

        showPerformanceInfo(duration) {
          const performanceInfo = document.getElementById('performanceInfo');
          const performanceDetails = document.getElementById('performanceDetails');

          performanceDetails.textContent = `Geração completa em ${duration}ms (Domain: ~2ms, Infrastructure: ~${(duration - 2).toFixed(0)}ms)`;
          performanceInfo.style.display = 'block';
        }

        applyPreset(presetName) {
          // Remove active class from all buttons
          document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.classList.remove('active');
          });

          // Add active class to clicked button
          document.querySelector(`[data-preset="${presetName}"]`).classList.add('active');

          const presets = {
            modern: {
              dotsType: 'rounded',
              dotsColor: '#667eea',
              cornerSquareType: 'extra-rounded',
              cornerSquareColor: '#764ba2',
              cornerDotType: 'rounded',
              cornerDotColor: '#667eea',
              backgroundColor: '#ffffff',
            },
            classic: {
              dotsType: 'square',
              dotsColor: '#000000',
              cornerSquareType: 'square',
              cornerSquareColor: '#000000',
              cornerDotType: 'square',
              cornerDotColor: '#000000',
              backgroundColor: '#ffffff',
            },
            elegant: {
              dotsType: 'classy-rounded',
              dotsColor: '#2c3e50',
              cornerSquareType: 'rounded',
              cornerSquareColor: '#34495e',
              cornerDotType: 'rounded',
              cornerDotColor: '#2c3e50',
              backgroundColor: '#ecf0f1',
            },
            vibrant: {
              dotsType: 'dots',
              dotsColor: '#e74c3c',
              cornerSquareType: 'extra-rounded',
              cornerSquareColor: '#3498db',
              cornerDotType: 'dot',
              cornerDotColor: '#f39c12',
              backgroundColor: '#ffffff',
            },
            circular: {
              dotsType: 'rounded',
              dotsColor: '#2c3e50',
              cornerSquareType: 'dot',
              cornerSquareColor: '#e74c3c',
              cornerDotType: 'dot',
              cornerDotColor: '#3498db',
              backgroundColor: '#ffffff',
            },
          };

          const preset = presets[presetName];
          if (preset) {
            this.elements.dotsType.value = preset.dotsType;
            this.elements.dotsColor.value = preset.dotsColor;
            this.elements.cornerSquareType.value = preset.cornerSquareType;
            this.elements.cornerSquareColor.value = preset.cornerSquareColor;
            this.elements.cornerDotType.value = preset.cornerDotType;
            this.elements.cornerDotColor.value = preset.cornerDotColor;
            this.elements.backgroundColor.value = preset.backgroundColor;

            // Regenerate QR if already displayed and customization is active
            if (this.customizationActive) {
              this.regenerateQRCode();
            }
          }

          this.currentPreset = presetName;
        }

        // Utility methods
        delay(ms) {
          return new Promise(resolve => setTimeout(resolve, ms));
        }

        applyCPFMask(value) {
          return value
            .replace(/\D/g, '')
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d)/, '$1.$2')
            .replace(/(\d{3})(\d{1,2})/, '$1-$2')
            .replace(/(-\d{2})\d+?$/, '$1');
        }

        applyPhoneMask(value) {
          return value
            .replace(/\D/g, '')
            .replace(/(\d{2})(\d)/, '($1) $2')
            .replace(/(\d{5})(\d)/, '$1-$2')
            .replace(/(-\d{4})\d+?$/, '$1');
        }

        applyCurrencyMask(value) {
          let v = value.replace(/\D/g, '');
          v = (parseInt(v) / 100).toFixed(2) + '';
          v = v.replace('.', ',');
          v = v.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
          return 'R$ ' + v;
        }

        parseAmount(value) {
          if (!value || value === 'R$ 0,00') return 0;
          return parseFloat(value.replace('R$ ', '').replace(/\./g, '').replace(',', '.')) || 0;
        }

        validateCPF(cpf) {
          cpf = cpf.replace(/\D/g, '');
          if (cpf.length !== 11) return false;

          // Simplified CPF validation for demo
          const invalidCPFs = [
            '00000000000',
            '11111111111',
            '22222222222',
            '33333333333',
            '44444444444',
            '55555555555',
            '66666666666',
            '77777777777',
            '88888888888',
            '99999999999',
          ];
          if (invalidCPFs.includes(cpf)) return false;

          return true; // Simplified validation
        }

        validateEmail(email) {
          const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          return re.test(email);
        }

        validatePhone(phone) {
          const cleanPhone = phone.replace(/\D/g, '');
          return cleanPhone.length >= 10;
        }
      }

      // Global functions for buttons
      window.downloadQRCode = function () {
        const qrImg = document.querySelector('.qr-code');
        if (qrImg) {
          const link = document.createElement('a');
          link.download = `qr-code-pix-${Date.now()}.png`;
          link.href = qrImg.src;
          link.click();
        }
      };

      window.copyBRCode = function () {
        const brCode = document.getElementById('brCode').textContent;
        navigator.clipboard.writeText(brCode).then(() => {
          alert('BR Code copiado para a área de transferência!');
        });
      };

      window.resetForm = function () {
        document.getElementById('pixForm').reset();
        document.getElementById('qrContainer').innerHTML = `
                <div class="qr-placeholder">
                    <p>🎯 QR Code aparecerá aqui</p>
                    <p style="font-size: 14px; margin-top: 10px;">Preencha os dados e clique em "Gerar QR Code"</p>
                </div>
            `;
        document.getElementById('brCodeContainer').style.display = 'none';
        document.getElementById('actions').style.display = 'none';
        document.getElementById('performanceInfo').style.display = 'none';

        // Reset character counters
        document.querySelectorAll('.char-counter').forEach(counter => {
          counter.textContent = '0/25 caracteres';
        });

        // Reset validation
        document.getElementById('keyValidation').textContent = '';
        document.getElementById('keyValidation').className = 'validation-message';
      };

      // Initialize the application
      document.addEventListener('DOMContentLoaded', () => {
        new PIXFormManager();
        console.log('✅ PIX QR Generator initialized with Clean Architecture demo');
      });
    </script>
  </body>
</html>
